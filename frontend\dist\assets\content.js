function c(e){const t=new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window});e.dispatchEvent(t)}function a(){const e=document.getElementById("btnSubmit");e&&!e.disabled?(console.log("RefNo Filler: Found enabled final submit button. Clicking with robust method."),c(e)):console.log("RefNo Filler: Could not find or click the final page submit button.")}function s(){let e=0;const t=60,o=setInterval(()=>{const n=document.querySelector("#submit.fa.fa-check.p-1.img-action");if(n){clearInterval(o),console.log("RefNo Filler: Found CAPTCHA submit button. Now waiting for the user to solve it."),n.addEventListener("click",()=>{console.log("RefNo Filler: CAPTCHA has been submitted by the user. Starting the 5-second timer before final submission."),setTimeout(a,5e3)},{once:!0});return}e++,e>=t&&(clearInterval(o),console.log("RefNo Filler: Timed out waiting for the CAPTCHA submit button to appear."))},100)}function i(){const e=document.getElementById("btnVerify");e?(console.log("RefNo Filler: Found 'Verify' button. Clicking it to reveal CAPTCHA."),e.click(),s()):console.log("RefNo Filler: The initial 'Verify' button was not found on the page.")}function l(e){if(!e)return!1;const t=document.getElementById("refNo");return t?(t.value=e,!0):!1}function r(e){if(!e)return!1;const t=document.getElementById("dob");return t?(t.removeAttribute("readonly"),t.value=e,t.dispatchEvent(new Event("input",{bubbles:!0})),t.dispatchEvent(new Event("change",{bubbles:!0})),!0):!1}chrome.storage.local.get(["autoFillEnabled","savedRefNo","savedDob"],e=>{if(e.autoFillEnabled&&e.savedRefNo&&e.savedDob){const t=l(e.savedRefNo),o=r(e.savedDob);t&&o&&setTimeout(i,100)}});chrome.runtime.onMessage.addListener((e,t,o)=>{if(e.type==="FILL_INPUT"&&e.payload.refNo&&e.payload.dob){const n=l(e.payload.refNo),u=r(e.payload.dob);n&&u&&setTimeout(i,100)}});

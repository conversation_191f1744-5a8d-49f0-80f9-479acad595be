{"version": 3, "file": "browser-polyfill.min.js", "mappings": "gSAMA,YAAY,CAEZ,GAAI,EAAEA,UAAU,CAACC,MAAM,EAAID,UAAU,CAACC,MAAM,CAACC,OAAO,EAAIF,UAAU,CAACC,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,CACnF,KAAM,IAAIC,MAAK,CAAC,2DAA2D,CAAC,CAG9E,GAAI,EAAEJ,UAAU,CAACK,OAAO,EAAIL,UAAU,CAACK,OAAO,CAACH,OAAO,EAAIF,UAAU,CAACK,OAAO,CAACH,OAAO,CAACC,EAAE,CAAC,CAAE,CA+qCxFG,CAAM,CAACC,OAAO,CAAGC,CAvqCAC,CAAa,EAAI,CAIhC,KAAMC,EAAW,CAAG,CAClBC,MAAA,CAAU,CACRC,KAAA,CAAS,CACPC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDC,QAAA,CAAY,CACVF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDE,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDG,MAAA,CAAU,CACRJ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDI,SAAA,CAAa,CACXC,MAAA,CAAU,CACRN,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDE,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDM,WAAA,CAAe,CACbP,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDO,SAAA,CAAa,CACXR,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDQ,UAAA,CAAc,CACZT,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDS,OAAA,CAAW,CACTV,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDU,IAAA,CAAQ,CACNX,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDW,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDY,UAAA,CAAc,CACZb,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDa,MAAA,CAAU,CACRd,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDc,MAAA,CAAU,CACRf,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDe,aAAA,CAAiB,CACfC,OAAA,CAAW,CACTjB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDC,MAAA,CAAU,CACRnB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDE,uBAAA,CAA2B,CACzBpB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDoB,YAAA,CAAgB,CACdrB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDqB,QAAA,CAAY,CACVtB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsB,QAAA,CAAY,CACVvB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDuB,SAAA,CAAa,CACXxB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDwB,uBAAA,CAA2B,CACzBzB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDQ,YAAA,CAAgB,CACd1B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDS,OAAA,CAAW,CACT3B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD2B,QAAA,CAAY,CACV5B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDW,QAAA,CAAY,CACV7B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CACF,CAAC,CACDY,YAAA,CAAgB,CACdlB,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD8B,WAAA,CAAe,CACb/B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD+B,aAAA,CAAiB,CACfhC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDgC,eAAA,CAAmB,CACjBjC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDiC,cAAA,CAAkB,CAChBlC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDkC,aAAA,CAAiB,CACfnC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDmC,kBAAA,CAAsB,CACpBpC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDoC,eAAA,CAAmB,CACjBrC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDqC,gBAAA,CAAoB,CAClBtC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsC,QAAA,CAAY,CACVvC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDuC,QAAA,CAAY,CACVpC,MAAA,CAAU,CACRJ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDwC,YAAA,CAAgB,CACd7B,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDyC,SAAA,CAAa,CACX1C,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDc,MAAA,CAAU,CACRf,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD0C,OAAA,CAAW,CACTxC,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDG,MAAA,CAAU,CACRJ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD2C,kBAAA,CAAsB,CACpB5C,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDW,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD4C,GAAA,CAAO,CACL7C,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD6C,QAAA,CAAY,CACVC,eAAA,CAAmB,CACjBC,IAAA,CAAQ,CACNhD,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZgD,iBAAA,GACF,CACF,CAAC,CACDC,MAAA,CAAU,CACR5C,MAAA,CAAU,CACRN,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZgD,iBAAA,GACF,CAAC,CACDE,QAAA,CAAY,CACVC,iBAAA,CAAqB,CACnBpD,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CACF,CACF,CAAC,CACDoD,SAAA,CAAa,CACXC,MAAA,CAAU,CACRtD,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsD,QAAA,CAAY,CACVvD,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDuD,KAAA,CAAS,CACPxD,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDwD,WAAA,CAAe,CACbzD,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDyD,IAAA,CAAQ,CACN1D,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDyC,KAAA,CAAS,CACP3D,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD2D,UAAA,CAAc,CACZ5D,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD4D,MAAA,CAAU,CACR7D,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDa,MAAA,CAAU,CACRd,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD6D,IAAA,CAAQ,CACN9D,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CACF,CAAC,CACD6C,SAAA,CAAa,CACXC,yBAAA,CAA6B,CAC3BhE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDgE,wBAAA,CAA4B,CAC1BjE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDiE,OAAA,CAAW,CACTC,MAAA,CAAU,CACRnE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDmE,SAAA,CAAa,CACXpE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDoE,WAAA,CAAe,CACbrE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDqE,SAAA,CAAa,CACXtE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsE,SAAA,CAAa,CACXvE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDa,MAAA,CAAU,CACRd,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDuE,IAAA,CAAQ,CACNC,cAAA,CAAkB,CAChBzE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDyE,kBAAA,CAAsB,CACpB1E,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD0E,QAAA,CAAY,CACVC,iBAAA,CAAqB,CACnB5E,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD4E,IAAA,CAAQ,CACNC,UAAA,CAAc,CACZ9E,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD8E,UAAA,CAAc,CACZ5E,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDG,MAAA,CAAU,CACRJ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD+E,OAAA,CAAW,CACThF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDgF,UAAA,CAAc,CACZjF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDiF,aAAA,CAAiB,CACflF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDkF,aAAA,CAAiB,CACfpF,KAAA,CAAS,CACPC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDK,MAAA,CAAU,CACRN,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDG,MAAA,CAAU,CACRJ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDmF,kBAAA,CAAsB,CACpBpF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDc,MAAA,CAAU,CACRf,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDoF,UAAA,CAAc,CACZ/D,QAAA,CAAY,CACVtB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsB,QAAA,CAAY,CACVvB,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDqF,IAAA,CAAQ,CACNtF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDS,OAAA,CAAW,CACT3B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD2B,QAAA,CAAY,CACV5B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACDW,QAAA,CAAY,CACV7B,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CAAC,CACD4C,IAAA,CAAQ,CACN9D,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CAAC,CACZiB,oBAAA,GACF,CACF,CAAC,CACDqE,WAAA,CAAe,CACbC,QAAA,CAAY,CACVxF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDG,MAAA,CAAU,CACRJ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDW,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDwF,OAAA,CAAW,CACTzF,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDZ,OAAA,CAAW,CACTqG,iBAAA,CAAqB,CACnB1F,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD0F,eAAA,CAAmB,CACjB3F,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD2F,eAAA,CAAmB,CACjB5F,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD4F,kBAAA,CAAsB,CACpB7F,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD6F,WAAA,CAAe,CACb9F,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD8F,iBAAA,CAAqB,CACnB/F,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD+F,eAAA,CAAmB,CACjBhG,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDgG,QAAA,CAAY,CACVC,UAAA,CAAc,CACZlG,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDkG,iBAAA,CAAqB,CACnBnG,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDmG,OAAA,CAAW,CACTpG,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDoG,OAAA,CAAW,CACTC,KAAA,CAAS,CACPvG,KAAA,CAAS,CACPC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDE,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsG,aAAA,CAAiB,CACfvG,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDW,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD4C,GAAA,CAAO,CACL7C,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDuG,OAAA,CAAW,CACTrG,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsG,aAAA,CAAiB,CACfvG,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDwG,IAAA,CAAQ,CACN1G,KAAA,CAAS,CACPC,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDE,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsG,aAAA,CAAiB,CACfvG,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDW,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD4C,GAAA,CAAO,CACL7C,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CACF,CAAC,CACDyG,IAAA,CAAQ,CACNC,iBAAA,CAAqB,CACnB3G,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDK,MAAA,CAAU,CACRN,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDwE,cAAA,CAAkB,CAChBzE,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD2G,OAAA,CAAW,CACT5G,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD4G,SAAA,CAAa,CACX7G,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD6G,aAAA,CAAiB,CACf9G,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDE,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD8G,UAAA,CAAc,CACZ/G,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD+G,OAAA,CAAW,CACThH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDgH,eAAA,CAAmB,CACjBjH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDiH,MAAA,CAAU,CACRlH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDkH,SAAA,CAAa,CACXnH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDmH,SAAA,CAAa,CACXpH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDoH,SAAA,CAAa,CACXrH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDU,IAAA,CAAQ,CACNX,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDqH,KAAA,CAAS,CACPtH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDsH,MAAA,CAAU,CACRvH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDW,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDuH,SAAA,CAAa,CACXxH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD6F,WAAA,CAAe,CACb9F,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDwH,OAAA,CAAW,CACTzH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDyH,eAAA,CAAmB,CACjB1H,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDc,MAAA,CAAU,CACRf,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD0H,QAAA,CAAY,CACVxH,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD2H,aAAA,CAAiB,CACfC,YAAA,CAAgB,CACd7H,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD6H,QAAA,CAAY,CACV9H,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACD8H,UAAA,CAAc,CACZC,sBAAA,CAA0B,CACxBhI,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CAAC,CACDgI,OAAA,CAAW,CACT3H,MAAA,CAAU,CACRN,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDE,GAAA,CAAO,CACLH,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDG,MAAA,CAAU,CACRJ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACD8G,UAAA,CAAc,CACZ/G,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDiI,cAAA,CAAkB,CAChBlI,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDW,MAAA,CAAU,CACRZ,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CAAC,CACDc,MAAA,CAAU,CACRf,OAAA,CAAW,CAAC,CACZC,OAAA,CAAW,CACb,CACF,CACF,CAAC,CAED,GAAwC,CAAC,GAArCkI,MAAM,CAACC,IAAI,CAACvI,CAAW,CAAC,CAACwI,MAAY,CACvC,KAAM,IAAI9I,MAAK,CAAC,6DAA6D,CAAC,CAahF,KAAM+I,EAAc,QAASC,QAAQ,CACnCC,WAAWA,CAACC,CAAU,CAAEC,CAAK,OAAY,CAAE,CACzC,KAAK,CAACA,CAAK,CAAC,CACZ,IAAI,CAACD,UAAU,CAAGA,CACpB,CAEAtI,GAAGA,CAACwI,CAAG,CAAE,CAKP,MAJK,KAAI,CAACC,GAAG,CAACD,CAAG,CAAC,EAChB,IAAI,CAAC9F,GAAG,CAAC8F,CAAG,CAAE,IAAI,CAACF,UAAU,CAACE,CAAG,CAAC,CAAC,CAG9B,KAAK,CAACxI,GAAG,CAACwI,CAAG,CACtB,CACF,MASME,EAAU,CAAGC,CAAK,EACfA,CAAK,EAAqB,QAAQ,EAAzB,MAAOA,EAAkB,EAA0B,UAAU,EAAhC,MAAOA,EAAK,CAACC,IAC3D,CAiCKC,CAAY,CAAGA,CAACC,CAAO,CAAEC,CAAQ,GAC9B,CAAC,GAAGC,CAAY,GAAK,CACtBvJ,CAAa,CAACP,OAAO,CAAC+J,SAAS,CACjCH,CAAO,CAACI,MAAM,CAAC,GAAI9J,MAAK,CAACK,CAAa,CAACP,OAAO,CAAC+J,SAAS,CAACE,OAAO,CAAC,CAAC,CACzDJ,CAAQ,CAACjG,iBAAiB,EACF,CAAC,EAAxBkG,CAAY,CAACd,MAAW,EAAIa,MAAQ,CAACjG,iBAA4B,CAC3EgG,CAAO,CAACM,OAAO,CAACJ,CAAY,CAAC,CAAC,CAAC,CAAC,CAEhCF,CAAO,CAACM,OAAO,CAACJ,CAAY,CAEhC,CACD,CAEKK,CAAkB,CAAIC,CAAO,EAAgB,CAAC,EAAZA,CAAY,CAAG,UAAU,CAAG,WAAW,CA4BzEC,CAAiB,CAAGA,CAACC,CAAI,CAAET,CAAQ,GAChC,SAA8BU,CAAM,CAAE,GAAGC,CAAI,CAAE,CACpD,GAAIA,CAAI,CAACxB,MAAM,CAAGa,CAAQ,CAAClJ,OAAO,CAChC,KAAM,IAAIT,MAAK,CAAE,qBAAoB2J,CAAQ,CAAClJ,OAAQ,IAAGwJ,CAAkB,CAACN,CAAQ,CAAClJ,OAAO,CAAE,QAAO2J,CAAK,WAAUE,CAAI,CAACxB,MAAO,EAAC,CAAC,CAGpI,GAAIwB,CAAI,CAACxB,MAAM,CAAGa,CAAQ,CAACjJ,OAAO,CAChC,KAAM,IAAIV,MAAK,CAAE,oBAAmB2J,CAAQ,CAACjJ,OAAQ,IAAGuJ,CAAkB,CAACN,CAAQ,CAACjJ,OAAO,CAAE,QAAO0J,CAAK,WAAUE,CAAI,CAACxB,MAAO,EAAC,CAAC,CAGnI,MAAO,IAAIyB,QAAO,CAAC,CAACP,CAAO,CAAEF,CAAM,GAAK,CACtC,GAAIH,CAAQ,CAAChI,oBAAoB,CAI/B,GAAI,CACF0I,CAAM,CAACD,CAAI,CAAC,CAAC,GAAGE,CAAI,CAAEb,CAAY,CAAC,CAACO,OAAO,CAAPA,CAAO,CAAEF,QAAM,CAAC,CAAEH,CAAQ,CAAC,CACjE,CAAE,MAAOa,CAAO,CAAE,CAChBC,OAAO,CAACC,IAAI,CAAE,GAAEN,CAAK,8DAA6D,CACrE,8CAA8C,CAAEI,CAAO,CAAC,CAErEH,CAAM,CAACD,CAAI,CAAC,CAAC,GAAGE,CAAI,CAAC,CAIrBX,CAAQ,CAAChI,oBAAoB,GAAQ,CACrCgI,CAAQ,CAACgB,UAAU,GAAO,CAE1BX,CAAO,CAAC,CACV,KACSL,EAAQ,CAACgB,UAAU,EAC5BN,CAAM,CAACD,CAAI,CAAC,CAAC,GAAGE,CAAI,CAAC,CACrBN,CAAO,CAAC,CAAC,EAETK,CAAM,CAACD,CAAI,CAAC,CAAC,GAAGE,CAAI,CAAEb,CAAY,CAAC,CAACO,OAAO,CAAPA,CAAO,CAAEF,QAAM,CAAC,CAAEH,CAAQ,CAAC,CAEnE,CAAC,CACH,CACD,CAqBKiB,CAAU,CAAGA,CAACP,CAAM,CAAEQ,CAAM,CAAEC,CAAO,GAClC,GAAIC,MAAK,CAACF,CAAM,CAAE,CACvBG,KAAKA,CAACC,CAAY,CAAEC,CAAO,CAAEZ,CAAI,CAAE,CACjC,MAAOQ,EAAO,CAACK,IAAI,CAACD,CAAO,CAAEb,CAAM,CAAE,GAAGC,CAAI,CAC9C,CACF,CAAC,CACF,CAED,GAAIc,EAAc,CAAGC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC1C,MAAM,CAAC2C,SAAS,CAACH,cAAc,CAAC,MAyBlEI,EAAU,CAAGA,CAACnB,CAAM,CAAEoB,CAAQ,CAAG,CAAC,CAAC,CAAE9B,CAAQ,CAAG,CAAC,CAAC,GAAK,IACvD+B,EAAK,CAAG9C,MAAM,CAAC7H,MAAM,CAAC,IAAI,CAAC,CA6F3B4K,CAAW,CAAG/C,MAAM,CAAC7H,MAAM,CAACsJ,CAAM,CAAC,CACvC,MAAO,IAAIU,MAAK,CAACY,CAAW,CA7Fb,CACbtC,GAAGA,CAACsC,CAAW,CAAEC,CAAI,CAAE,CACrB,MAAOA,EAAI,GAAIvB,EAAM,EAAIuB,CAAI,GAAIF,EACnC,CAAC,CAED9K,GAAGA,CAAC+K,CAAW,CAAEC,CAAI,CAAY,CAC/B,GAAIA,CAAI,GAAIF,EAAK,CACf,MAAOA,EAAK,CAACE,CAAI,CAAC,CAGpB,GAAI,EAAEA,CAAI,GAAIvB,EAAM,CAAC,CACnB,OAGF,GAAId,EAAK,CAAGc,CAAM,CAACuB,CAAI,CAAC,CAExB,GAAqB,UAAU,EAA3B,MAAOrC,EAAoB,EAI7B,GAA8B,UAAU,EAApC,MAAOkC,EAAQ,CAACG,CAAI,CAAgB,CAEtCrC,CAAK,CAAGqB,CAAU,CAACP,CAAM,CAAEA,CAAM,CAACuB,CAAI,CAAC,CAAEH,CAAQ,CAACG,CAAI,CAAC,CAAC,KACnD,IAAIR,CAAc,CAACzB,CAAQ,CAAEiC,CAAI,CAAC,CAAE,CAGzC,GAAId,EAAO,CAAGX,CAAiB,CAACyB,CAAI,CAAEjC,CAAQ,CAACiC,CAAI,CAAC,CAAC,CACrDrC,CAAK,CAAGqB,CAAU,CAACP,CAAM,CAAEA,CAAM,CAACuB,CAAI,CAAC,CAAEd,CAAO,CAClD,CAAC,IAGCvB,EAAK,CAAGA,CAAK,CAAC+B,IAAI,CAACjB,CAAM,CAAC,CAC5B,KACK,IAAqB,QAAQ,EAAzB,MAAOd,EAAkB,EAAc,IAAI,GAAdA,CAAc,GAC1C6B,CAAc,CAACK,CAAQ,CAAEG,CAAI,CAAC,EAC9BR,CAAc,CAACzB,CAAQ,CAAEiC,CAAI,CAAC,CAAC,CAIzCrC,CAAK,CAAGiC,CAAU,CAACjC,CAAK,CAAEkC,CAAQ,CAACG,CAAI,CAAC,CAAEjC,CAAQ,CAACiC,CAAI,CAAC,CAAC,KACpD,IAAIR,CAAc,CAACzB,CAAQ,CAAE,GAAG,CAAC,CAEtCJ,CAAK,CAAGiC,CAAU,CAACjC,CAAK,CAAEkC,CAAQ,CAACG,CAAI,CAAC,CAAEjC,CAAQ,CAAC,GAAG,CAAC,CAAC,KAexD,OAXAf,OAAM,CAACiD,cAAc,CAACH,CAAK,CAAEE,CAAI,CAAE,CACjCE,YAAY,GAAM,CAClBC,UAAU,GAAM,CAChBnL,GAAGA,EAAG,CACJ,MAAOyJ,EAAM,CAACuB,CAAI,CACpB,CAAC,CACDtI,GAAGA,CAACiG,CAAK,CAAE,CACTc,CAAM,CAACuB,CAAI,CAAC,CAAGrC,CACjB,CACF,CAAC,CAAC,CAEKA,CAAK,CAId,MADAmC,EAAK,CAACE,CAAI,CAAC,CAAGrC,CAAK,CACZA,CACT,CAAC,CAEDjG,GAAGA,CAACqI,CAAW,CAAEC,CAAI,CAAErC,CAAK,CAAY,CAMtC,MALIqC,EAAI,GAAIF,EAAK,CACfA,CAAK,CAACE,CAAI,CAAC,CAAGrC,CAAK,CAEnBc,CAAM,CAACuB,CAAI,CAAC,CAAGrC,CAAK,GAGxB,CAAC,CAEDsC,cAAcA,CAACF,CAAW,CAAEC,CAAI,CAAEI,CAAI,CAAE,CACtC,MAAOC,QAAO,CAACJ,cAAc,CAACH,CAAK,CAAEE,CAAI,CAAEI,CAAI,CACjD,CAAC,CAEDE,cAAcA,CAACP,CAAW,CAAEC,CAAI,CAAE,CAChC,MAAOK,QAAO,CAACC,cAAc,CAACR,CAAK,CAAEE,CAAI,CAC3C,CACF,CAasC,CACxC,CAAC,CAkBKO,CAAS,CAAGC,CAAU,GAAK,CAC/BC,WAAWA,CAAChC,CAAM,CAAEiC,CAAQ,CAAE,GAAGhC,CAAI,CAAE,CACrCD,CAAM,CAACgC,WAAW,CAACD,CAAU,CAACxL,GAAG,CAAC0L,CAAQ,CAAC,CAAE,GAAGhC,CAAI,CACtD,CAAC,CAEDiC,WAAWA,CAAClC,CAAM,CAAEiC,CAAQ,CAAE,CAC5B,MAAOjC,EAAM,CAACkC,WAAW,CAACH,CAAU,CAACxL,GAAG,CAAC0L,CAAQ,CAAC,CACpD,CAAC,CAEDE,cAAcA,CAACnC,CAAM,CAAEiC,CAAQ,CAAE,CAC/BjC,CAAM,CAACmC,cAAc,CAACJ,CAAU,CAACxL,GAAG,CAAC0L,CAAQ,CAAC,CAChD,CACF,CAAC,CAAC,CAEIG,CAAyB,CAAG,GAAI1D,EAAc,CAACuD,CAAQ,EACnC,UAAU,EAA9B,MAAOA,EAAuB,CAY3B,SAA2BI,CAAG,CAAE,CACrC,KAAMC,EAAU,CAAGnB,CAAU,CAACkB,CAAG,CAAE,CAAC,CAAC,CAAiB,CACpDE,UAAU,CAAE,CACVnM,OAAO,CAAE,CAAC,CACVC,OAAO,CAAE,CACX,CACF,CAAC,CAAC,CACF4L,CAAQ,CAACK,CAAU,CACrB,CAAC,CAnBQL,CAoBV,CAAC,CAEIO,CAAiB,CAAG,GAAI9D,EAAc,CAACuD,CAAQ,EAC3B,UAAU,EAA9B,MAAOA,EAAuB,CAqB3B,SAAmBvC,CAAO,CAAE+C,CAAM,CAAEC,CAAY,CAAE,IAGnDC,EAAmB,CAQnBC,CAAM,CAVNC,CAAmB,GAAQ,CAG3BC,CAAmB,CAAG,GAAI5C,QAAO,CAACP,CAAO,EAAI,CAC/CgD,CAAmB,CAAG,QAAAA,CAASI,CAAQ,CAAE,CACvCF,CAAmB,GAAO,CAC1BlD,CAAO,CAACoD,CAAQ,CAClB,CACF,CAAC,CAAC,CAGF,GAAI,CACFH,CAAM,CAAGX,CAAQ,CAACvC,CAAO,CAAE+C,CAAM,CAAEE,CAAmB,CACxD,CAAE,MAAOK,CAAG,CAAE,CACZJ,CAAM,CAAG1C,OAAO,CAACT,MAAM,CAACuD,CAAG,CAC7B,CAEA,KAAMC,EAAgB,CAAGL,MAAe,EAAI3D,CAAU,CAAC2D,CAAM,CAAC,CAK9D,GAAIA,MAAe,EAAI,CAACK,CAAgB,EAAI,CAACJ,CAAmB,CAC9D,SAOF,KAAMK,EAAkB,CAAI7D,CAAO,EAAK,CACtCA,CAAO,CAACF,IAAI,CAACgE,CAAG,EAAI,CAElBT,CAAY,CAACS,CAAG,CAClB,CAAC,CAAEC,CAAK,EAAI,CAGV,GAAI1D,EAAO,CAGTA,CAAO,CAFL0D,CAAK,GAAKA,CAAK,WAAYzN,MAAK,EACP,QAAQ,EAAjC,MAAOyN,EAAK,CAAC1D,OAAoB,CAAC,CAC1B0D,CAAK,CAAC1D,OAAO,CAEb,8BAA8B,CAG1CgD,CAAY,CAAC,CACXW,iCAAiC,GAAM,CACvC3D,SACF,CAAC,CACH,CAAC,CAAC,CAAC4D,KAAK,CAACN,CAAG,EAAI,CAEd5C,OAAO,CAACgD,KAAK,CAAC,yCAAyC,CAAEJ,CAAG,CAC9D,CAAC,CACH,CAAC,CAYD,MAPIC,EAAgB,CAClBC,CAAkB,CAACN,CAAM,CAAC,CAE1BM,CAAkB,CAACJ,CAAmB,CAAC,GAK3C,CAAC,CAvFQb,CAwFV,CAAC,CAEIsB,CAA0B,CAAGA,CAAC,CAAC9D,MAAM,CAANA,CAAM,CAAEE,SAAO,CAAC,CAAE6D,CAAK,GAAK,CAC3DxN,CAAa,CAACP,OAAO,CAAC+J,SAAS,CAI7BxJ,CAAa,CAACP,OAAO,CAAC+J,SAAS,CAACE,OAAO,GAjnCQ,yDAinC6C,CAC9FC,CAAO,CAAC,CAAC,CAETF,CAAM,CAAC,GAAI9J,MAAK,CAACK,CAAa,CAACP,OAAO,CAAC+J,SAAS,CAACE,OAAO,CAAC,CAAC,CAEnD8D,CAAK,EAAIA,CAAK,CAACH,iCAAiC,CAGzD5D,CAAM,CAAC,GAAI9J,MAAK,CAAC6N,CAAK,CAAC9D,OAAO,CAAC,CAAC,CAEhCC,CAAO,CAAC6D,CAAK,CAEjB,CAAC,CAEKC,CAAkB,CAAGA,CAAC1D,CAAI,CAAET,CAAQ,CAAEoE,CAAe,CAAE,GAAGzD,CAAI,GAAK,CACvE,GAAIA,CAAI,CAACxB,MAAM,CAAGa,CAAQ,CAAClJ,OAAO,CAChC,KAAM,IAAIT,MAAK,CAAE,qBAAoB2J,CAAQ,CAAClJ,OAAQ,IAAGwJ,CAAkB,CAACN,CAAQ,CAAClJ,OAAO,CAAE,QAAO2J,CAAK,WAAUE,CAAI,CAACxB,MAAO,EAAC,CAAC,CAGpI,GAAIwB,CAAI,CAACxB,MAAM,CAAGa,CAAQ,CAACjJ,OAAO,CAChC,KAAM,IAAIV,MAAK,CAAE,oBAAmB2J,CAAQ,CAACjJ,OAAQ,IAAGuJ,CAAkB,CAACN,CAAQ,CAACjJ,OAAO,CAAE,QAAO0J,CAAK,WAAUE,CAAI,CAACxB,MAAO,EAAC,CAAC,CAGnI,MAAO,IAAIyB,QAAO,CAAC,CAACP,CAAO,CAAEF,CAAM,GAAK,CACtC,KAAMkE,EAAS,CAAGJ,CAA0B,CAACtC,IAAI,CAAC,IAAI,CAAE,CAACtB,OAAO,CAAPA,CAAO,CAAEF,QAAM,CAAC,CAAC,CAC1EQ,CAAI,CAAC2D,IAAI,CAACD,CAAS,CAAC,CACpBD,CAAe,CAACxH,WAAW,CAAC,GAAG+D,CAAI,CACrC,CAAC,CACH,CAAC,CAEK4D,CAAc,CAAG,CACrB3K,QAAQ,CAAE,CACR4K,OAAO,CAAE,CACPC,iBAAiB,CAAEjC,CAAS,CAACM,CAAyB,CACxD,CACF,CAAC,CACD3M,OAAO,CAAE,CACPuO,SAAS,CAAElC,CAAS,CAACU,CAAiB,CAAC,CACvCyB,iBAAiB,CAAEnC,CAAS,CAACU,CAAiB,CAAC,CAC/CtG,WAAW,CAAEuH,CAAkB,CAACxC,IAAI,CAAC,IAAI,CAAE,aAAa,CAAE,CAAC7K,OAAO,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAC,CAAC,CACpF,CAAC,CACDyG,IAAI,CAAE,CACJZ,WAAW,CAAEuH,CAAkB,CAACxC,IAAI,CAAC,IAAI,CAAE,aAAa,CAAE,CAAC7K,OAAO,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAC,CAAC,CACpF,CACF,CAAC,CACK6N,CAAe,CAAG,CACtB/N,KAAK,CAAE,CAACC,OAAO,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAC,CAAC,CAC/BE,GAAG,CAAE,CAACH,OAAO,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAC,CAAC,CAC7B4C,GAAG,CAAE,CAAC7C,OAAO,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAC,CAC9B,CAAC,CAOD,MANAJ,EAAW,CAACkO,OAAO,CAAG,CACpBL,OAAO,CAAE,CAACM,GAAA,CAAKF,CAAe,CAAC,CAC/BG,QAAQ,CAAE,CAACD,GAAA,CAAKF,CAAe,CAAC,CAChCI,QAAQ,CAAE,CAACF,GAAA,CAAKF,CAAe,CACjC,CAAC,CAEM/C,CAAU,CAACnL,CAAa,CAAE6N,CAAc,CAAE5N,CAAW,CAC9D,CAAC,EAIyBT,MAAM,CAClC,CAAC,IACCK,EAAM,CAACC,OAAO,CAAGP,UAAU,CAACK,OAC7B", "names": ["globalThis", "chrome", "runtime", "id", "Error", "browser", "module", "exports", "wrapAPIs", "extensionAPIs", "apiMetadata", "alarms", "clear", "min<PERSON><PERSON>s", "maxArgs", "clearAll", "get", "getAll", "bookmarks", "create", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRecent", "getSubTree", "getTree", "move", "remove", "removeTree", "search", "update", "browserAction", "disable", "fallback<PERSON><PERSON><PERSON><PERSON>Call<PERSON>", "enable", "getBadgeBackgroundColor", "getBadgeText", "getPopup", "getTitle", "openPopup", "setBadgeBackgroundColor", "setBadgeText", "setIcon", "setPopup", "setTitle", "browsingData", "removeCache", "removeCookies", "removeDownloads", "removeFormData", "removeHistory", "removeLocalStorage", "removePasswords", "removePluginData", "settings", "commands", "contextMenus", "removeAll", "cookies", "getAllCookieStores", "set", "devtools", "inspectedWindow", "eval", "singleCallbackArg", "panels", "elements", "createSidebarPane", "downloads", "cancel", "download", "erase", "getFileIcon", "open", "pause", "removeFile", "resume", "show", "extension", "isAllowedFileSchemeAccess", "isAllowedIncognitoAccess", "history", "addUrl", "deleteAll", "deleteRange", "deleteUrl", "getVisits", "i18n", "detectLanguage", "getAcceptLanguages", "identity", "launchWebAuthFlow", "idle", "queryState", "management", "getSelf", "setEnabled", "uninstallSelf", "notifications", "getPermissionLevel", "pageAction", "hide", "permissions", "contains", "request", "getBackgroundPage", "getPlatformInfo", "openOptionsPage", "requestUpdateCheck", "sendMessage", "sendNativeMessage", "setUninstallURL", "sessions", "getDevices", "getRecentlyClosed", "restore", "storage", "local", "getBytesInUse", "managed", "sync", "tabs", "captureVisibleTab", "discard", "duplicate", "executeScript", "get<PERSON>urrent", "getZoom", "getZoomSettings", "goBack", "goForward", "highlight", "insertCSS", "query", "reload", "removeCSS", "setZoom", "setZoomSettings", "topSites", "webNavigation", "getAllFrames", "getFrame", "webRequest", "handler<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "windows", "getLastFocused", "Object", "keys", "length", "DefaultWeakMap", "WeakMap", "constructor", "createItem", "items", "key", "has", "isThenable", "value", "then", "makeCallback", "promise", "metadata", "callback<PERSON><PERSON><PERSON>", "lastError", "reject", "message", "resolve", "pluralizeArguments", "numArgs", "wrapAsyncFunction", "name", "target", "args", "Promise", "cb<PERSON><PERSON>r", "console", "warn", "noCallback", "wrapMethod", "method", "wrapper", "Proxy", "apply", "targetMethod", "thisObj", "call", "hasOwnProperty", "Function", "bind", "prototype", "wrapObject", "wrappers", "cache", "proxyTarget", "prop", "defineProperty", "configurable", "enumerable", "desc", "Reflect", "deleteProperty", "wrapEvent", "wrapperMap", "addListener", "listener", "hasListener", "removeListener", "onRequestFinishedWrappers", "req", "wrappedReq", "get<PERSON>ontent", "onMessageWrappers", "sender", "sendResponse", "wrappedSendResponse", "result", "didCallSendResponse", "sendResponsePromise", "response", "err", "isResultThenable", "sendPromisedResult", "msg", "error", "__mozWebExtensionPolyfillReject__", "catch", "wrappedSendMessageCallback", "reply", "wrappedSendMessage", "apiNamespaceObj", "wrappedCb", "push", "staticWrappers", "network", "onRequestFinished", "onMessage", "onMessageExternal", "settingMetadata", "privacy", "\"*\"", "services", "websites"], "ignoreList": [], "sources": ["browser-polyfill.js"], "sourcesContent": ["/* webextension-polyfill - v0.12.0 - <PERSON>e May 14 2024 18:01:29 */\n/* -*- Mode: indent-tabs-mode: nil; js-indent-level: 2 -*- */\n/* vim: set sts=2 sw=2 et tw=80: */\n/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at http://mozilla.org/MPL/2.0/. */\n\"use strict\";\n\nif (!(globalThis.chrome && globalThis.chrome.runtime && globalThis.chrome.runtime.id)) {\n  throw new Error(\"This script should only be loaded in a browser extension.\");\n}\n\nif (!(globalThis.browser && globalThis.browser.runtime && globalThis.browser.runtime.id)) {\n  const CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE = \"The message port closed before a response was received.\";\n\n  // Wrapping the bulk of this polyfill in a one-time-use function is a minor\n  // optimization for Firefox. Since Spidermonkey does not fully parse the\n  // contents of a function until the first time it's called, and since it will\n  // never actually need to be called, this allows the polyfill to be included\n  // in Firefox nearly for free.\n  const wrapAPIs = extensionAPIs => {\n    // NOTE: apiMetadata is associated to the content of the api-metadata.json file\n    // at build time by replacing the following \"include\" with the content of the\n    // JSON file.\n    const apiMetadata = {\n      \"alarms\": {\n        \"clear\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"clearAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"get\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"bookmarks\": {\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getChildren\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getRecent\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getSubTree\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTree\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"move\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeTree\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"browserAction\": {\n        \"disable\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"enable\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"getBadgeBackgroundColor\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getBadgeText\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"openPopup\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"setBadgeBackgroundColor\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setBadgeText\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"setPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"browsingData\": {\n        \"remove\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"removeCache\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeCookies\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeDownloads\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeFormData\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeHistory\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeLocalStorage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removePasswords\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removePluginData\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"settings\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"commands\": {\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"contextMenus\": {\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"cookies\": {\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAllCookieStores\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"set\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"devtools\": {\n        \"inspectedWindow\": {\n          \"eval\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2,\n            \"singleCallbackArg\": false\n          }\n        },\n        \"panels\": {\n          \"create\": {\n            \"minArgs\": 3,\n            \"maxArgs\": 3,\n            \"singleCallbackArg\": true\n          },\n          \"elements\": {\n            \"createSidebarPane\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 1\n            }\n          }\n        }\n      },\n      \"downloads\": {\n        \"cancel\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"download\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"erase\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getFileIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"open\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"pause\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeFile\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"resume\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"show\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"extension\": {\n        \"isAllowedFileSchemeAccess\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"isAllowedIncognitoAccess\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"history\": {\n        \"addUrl\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"deleteAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"deleteRange\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"deleteUrl\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getVisits\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"i18n\": {\n        \"detectLanguage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAcceptLanguages\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"identity\": {\n        \"launchWebAuthFlow\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"idle\": {\n        \"queryState\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"management\": {\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getSelf\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"setEnabled\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"uninstallSelf\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        }\n      },\n      \"notifications\": {\n        \"clear\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getPermissionLevel\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"pageAction\": {\n        \"getPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"hide\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"setPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"show\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"permissions\": {\n        \"contains\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"request\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"runtime\": {\n        \"getBackgroundPage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getPlatformInfo\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"openOptionsPage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"requestUpdateCheck\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"sendMessage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 3\n        },\n        \"sendNativeMessage\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"setUninstallURL\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"sessions\": {\n        \"getDevices\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getRecentlyClosed\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"restore\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        }\n      },\n      \"storage\": {\n        \"local\": {\n          \"clear\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"set\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"managed\": {\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          }\n        },\n        \"sync\": {\n          \"clear\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"set\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        }\n      },\n      \"tabs\": {\n        \"captureVisibleTab\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 2\n        },\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"detectLanguage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"discard\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"duplicate\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"executeScript\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getCurrent\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getZoom\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getZoomSettings\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"goBack\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"goForward\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"highlight\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"insertCSS\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"move\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"query\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"reload\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 2\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeCSS\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"sendMessage\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 3\n        },\n        \"setZoom\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"setZoomSettings\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"update\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        }\n      },\n      \"topSites\": {\n        \"get\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"webNavigation\": {\n        \"getAllFrames\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getFrame\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"webRequest\": {\n        \"handlerBehaviorChanged\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"windows\": {\n        \"create\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getCurrent\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getLastFocused\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      }\n    };\n\n    if (Object.keys(apiMetadata).length === 0) {\n      throw new Error(\"api-metadata.json has not been included in browser-polyfill\");\n    }\n\n    /**\n     * A WeakMap subclass which creates and stores a value for any key which does\n     * not exist when accessed, but behaves exactly as an ordinary WeakMap\n     * otherwise.\n     *\n     * @param {function} createItem\n     *        A function which will be called in order to create the value for any\n     *        key which does not exist, the first time it is accessed. The\n     *        function receives, as its only argument, the key being created.\n     */\n    class DefaultWeakMap extends WeakMap {\n      constructor(createItem, items = undefined) {\n        super(items);\n        this.createItem = createItem;\n      }\n\n      get(key) {\n        if (!this.has(key)) {\n          this.set(key, this.createItem(key));\n        }\n\n        return super.get(key);\n      }\n    }\n\n    /**\n     * Returns true if the given object is an object with a `then` method, and can\n     * therefore be assumed to behave as a Promise.\n     *\n     * @param {*} value The value to test.\n     * @returns {boolean} True if the value is thenable.\n     */\n    const isThenable = value => {\n      return value && typeof value === \"object\" && typeof value.then === \"function\";\n    };\n\n    /**\n     * Creates and returns a function which, when called, will resolve or reject\n     * the given promise based on how it is called:\n     *\n     * - If, when called, `chrome.runtime.lastError` contains a non-null object,\n     *   the promise is rejected with that value.\n     * - If the function is called with exactly one argument, the promise is\n     *   resolved to that value.\n     * - Otherwise, the promise is resolved to an array containing all of the\n     *   function's arguments.\n     *\n     * @param {object} promise\n     *        An object containing the resolution and rejection functions of a\n     *        promise.\n     * @param {function} promise.resolve\n     *        The promise's resolution function.\n     * @param {function} promise.reject\n     *        The promise's rejection function.\n     * @param {object} metadata\n     *        Metadata about the wrapped method which has created the callback.\n     * @param {boolean} metadata.singleCallbackArg\n     *        Whether or not the promise is resolved with only the first\n     *        argument of the callback, alternatively an array of all the\n     *        callback arguments is resolved. By default, if the callback\n     *        function is invoked with only a single argument, that will be\n     *        resolved to the promise, while all arguments will be resolved as\n     *        an array if multiple are given.\n     *\n     * @returns {function}\n     *        The generated callback function.\n     */\n    const makeCallback = (promise, metadata) => {\n      return (...callbackArgs) => {\n        if (extensionAPIs.runtime.lastError) {\n          promise.reject(new Error(extensionAPIs.runtime.lastError.message));\n        } else if (metadata.singleCallbackArg ||\n                   (callbackArgs.length <= 1 && metadata.singleCallbackArg !== false)) {\n          promise.resolve(callbackArgs[0]);\n        } else {\n          promise.resolve(callbackArgs);\n        }\n      };\n    };\n\n    const pluralizeArguments = (numArgs) => numArgs == 1 ? \"argument\" : \"arguments\";\n\n    /**\n     * Creates a wrapper function for a method with the given name and metadata.\n     *\n     * @param {string} name\n     *        The name of the method which is being wrapped.\n     * @param {object} metadata\n     *        Metadata about the method being wrapped.\n     * @param {integer} metadata.minArgs\n     *        The minimum number of arguments which must be passed to the\n     *        function. If called with fewer than this number of arguments, the\n     *        wrapper will raise an exception.\n     * @param {integer} metadata.maxArgs\n     *        The maximum number of arguments which may be passed to the\n     *        function. If called with more than this number of arguments, the\n     *        wrapper will raise an exception.\n     * @param {boolean} metadata.singleCallbackArg\n     *        Whether or not the promise is resolved with only the first\n     *        argument of the callback, alternatively an array of all the\n     *        callback arguments is resolved. By default, if the callback\n     *        function is invoked with only a single argument, that will be\n     *        resolved to the promise, while all arguments will be resolved as\n     *        an array if multiple are given.\n     *\n     * @returns {function(object, ...*)}\n     *       The generated wrapper function.\n     */\n    const wrapAsyncFunction = (name, metadata) => {\n      return function asyncFunctionWrapper(target, ...args) {\n        if (args.length < metadata.minArgs) {\n          throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        if (args.length > metadata.maxArgs) {\n          throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        return new Promise((resolve, reject) => {\n          if (metadata.fallbackToNoCallback) {\n            // This API method has currently no callback on Chrome, but it return a promise on Firefox,\n            // and so the polyfill will try to call it with a callback first, and it will fallback\n            // to not passing the callback if the first call fails.\n            try {\n              target[name](...args, makeCallback({resolve, reject}, metadata));\n            } catch (cbError) {\n              console.warn(`${name} API method doesn't seem to support the callback parameter, ` +\n                           \"falling back to call it without a callback: \", cbError);\n\n              target[name](...args);\n\n              // Update the API method metadata, so that the next API calls will not try to\n              // use the unsupported callback anymore.\n              metadata.fallbackToNoCallback = false;\n              metadata.noCallback = true;\n\n              resolve();\n            }\n          } else if (metadata.noCallback) {\n            target[name](...args);\n            resolve();\n          } else {\n            target[name](...args, makeCallback({resolve, reject}, metadata));\n          }\n        });\n      };\n    };\n\n    /**\n     * Wraps an existing method of the target object, so that calls to it are\n     * intercepted by the given wrapper function. The wrapper function receives,\n     * as its first argument, the original `target` object, followed by each of\n     * the arguments passed to the original method.\n     *\n     * @param {object} target\n     *        The original target object that the wrapped method belongs to.\n     * @param {function} method\n     *        The method being wrapped. This is used as the target of the Proxy\n     *        object which is created to wrap the method.\n     * @param {function} wrapper\n     *        The wrapper function which is called in place of a direct invocation\n     *        of the wrapped method.\n     *\n     * @returns {Proxy<function>}\n     *        A Proxy object for the given method, which invokes the given wrapper\n     *        method in its place.\n     */\n    const wrapMethod = (target, method, wrapper) => {\n      return new Proxy(method, {\n        apply(targetMethod, thisObj, args) {\n          return wrapper.call(thisObj, target, ...args);\n        },\n      });\n    };\n\n    let hasOwnProperty = Function.call.bind(Object.prototype.hasOwnProperty);\n\n    /**\n     * Wraps an object in a Proxy which intercepts and wraps certain methods\n     * based on the given `wrappers` and `metadata` objects.\n     *\n     * @param {object} target\n     *        The target object to wrap.\n     *\n     * @param {object} [wrappers = {}]\n     *        An object tree containing wrapper functions for special cases. Any\n     *        function present in this object tree is called in place of the\n     *        method in the same location in the `target` object tree. These\n     *        wrapper methods are invoked as described in {@see wrapMethod}.\n     *\n     * @param {object} [metadata = {}]\n     *        An object tree containing metadata used to automatically generate\n     *        Promise-based wrapper functions for asynchronous. Any function in\n     *        the `target` object tree which has a corresponding metadata object\n     *        in the same location in the `metadata` tree is replaced with an\n     *        automatically-generated wrapper function, as described in\n     *        {@see wrapAsyncFunction}\n     *\n     * @returns {Proxy<object>}\n     */\n    const wrapObject = (target, wrappers = {}, metadata = {}) => {\n      let cache = Object.create(null);\n      let handlers = {\n        has(proxyTarget, prop) {\n          return prop in target || prop in cache;\n        },\n\n        get(proxyTarget, prop, receiver) {\n          if (prop in cache) {\n            return cache[prop];\n          }\n\n          if (!(prop in target)) {\n            return undefined;\n          }\n\n          let value = target[prop];\n\n          if (typeof value === \"function\") {\n            // This is a method on the underlying object. Check if we need to do\n            // any wrapping.\n\n            if (typeof wrappers[prop] === \"function\") {\n              // We have a special-case wrapper for this method.\n              value = wrapMethod(target, target[prop], wrappers[prop]);\n            } else if (hasOwnProperty(metadata, prop)) {\n              // This is an async method that we have metadata for. Create a\n              // Promise wrapper for it.\n              let wrapper = wrapAsyncFunction(prop, metadata[prop]);\n              value = wrapMethod(target, target[prop], wrapper);\n            } else {\n              // This is a method that we don't know or care about. Return the\n              // original method, bound to the underlying object.\n              value = value.bind(target);\n            }\n          } else if (typeof value === \"object\" && value !== null &&\n                     (hasOwnProperty(wrappers, prop) ||\n                      hasOwnProperty(metadata, prop))) {\n            // This is an object that we need to do some wrapping for the children\n            // of. Create a sub-object wrapper for it with the appropriate child\n            // metadata.\n            value = wrapObject(value, wrappers[prop], metadata[prop]);\n          } else if (hasOwnProperty(metadata, \"*\")) {\n            // Wrap all properties in * namespace.\n            value = wrapObject(value, wrappers[prop], metadata[\"*\"]);\n          } else {\n            // We don't need to do any wrapping for this property,\n            // so just forward all access to the underlying object.\n            Object.defineProperty(cache, prop, {\n              configurable: true,\n              enumerable: true,\n              get() {\n                return target[prop];\n              },\n              set(value) {\n                target[prop] = value;\n              },\n            });\n\n            return value;\n          }\n\n          cache[prop] = value;\n          return value;\n        },\n\n        set(proxyTarget, prop, value, receiver) {\n          if (prop in cache) {\n            cache[prop] = value;\n          } else {\n            target[prop] = value;\n          }\n          return true;\n        },\n\n        defineProperty(proxyTarget, prop, desc) {\n          return Reflect.defineProperty(cache, prop, desc);\n        },\n\n        deleteProperty(proxyTarget, prop) {\n          return Reflect.deleteProperty(cache, prop);\n        },\n      };\n\n      // Per contract of the Proxy API, the \"get\" proxy handler must return the\n      // original value of the target if that value is declared read-only and\n      // non-configurable. For this reason, we create an object with the\n      // prototype set to `target` instead of using `target` directly.\n      // Otherwise we cannot return a custom object for APIs that\n      // are declared read-only and non-configurable, such as `chrome.devtools`.\n      //\n      // The proxy handlers themselves will still use the original `target`\n      // instead of the `proxyTarget`, so that the methods and properties are\n      // dereferenced via the original targets.\n      let proxyTarget = Object.create(target);\n      return new Proxy(proxyTarget, handlers);\n    };\n\n    /**\n     * Creates a set of wrapper functions for an event object, which handles\n     * wrapping of listener functions that those messages are passed.\n     *\n     * A single wrapper is created for each listener function, and stored in a\n     * map. Subsequent calls to `addListener`, `hasListener`, or `removeListener`\n     * retrieve the original wrapper, so that  attempts to remove a\n     * previously-added listener work as expected.\n     *\n     * @param {DefaultWeakMap<function, function>} wrapperMap\n     *        A DefaultWeakMap object which will create the appropriate wrapper\n     *        for a given listener function when one does not exist, and retrieve\n     *        an existing one when it does.\n     *\n     * @returns {object}\n     */\n    const wrapEvent = wrapperMap => ({\n      addListener(target, listener, ...args) {\n        target.addListener(wrapperMap.get(listener), ...args);\n      },\n\n      hasListener(target, listener) {\n        return target.hasListener(wrapperMap.get(listener));\n      },\n\n      removeListener(target, listener) {\n        target.removeListener(wrapperMap.get(listener));\n      },\n    });\n\n    const onRequestFinishedWrappers = new DefaultWeakMap(listener => {\n      if (typeof listener !== \"function\") {\n        return listener;\n      }\n\n      /**\n       * Wraps an onRequestFinished listener function so that it will return a\n       * `getContent()` property which returns a `Promise` rather than using a\n       * callback API.\n       *\n       * @param {object} req\n       *        The HAR entry object representing the network request.\n       */\n      return function onRequestFinished(req) {\n        const wrappedReq = wrapObject(req, {} /* wrappers */, {\n          getContent: {\n            minArgs: 0,\n            maxArgs: 0,\n          },\n        });\n        listener(wrappedReq);\n      };\n    });\n\n    const onMessageWrappers = new DefaultWeakMap(listener => {\n      if (typeof listener !== \"function\") {\n        return listener;\n      }\n\n      /**\n       * Wraps a message listener function so that it may send responses based on\n       * its return value, rather than by returning a sentinel value and calling a\n       * callback. If the listener function returns a Promise, the response is\n       * sent when the promise either resolves or rejects.\n       *\n       * @param {*} message\n       *        The message sent by the other end of the channel.\n       * @param {object} sender\n       *        Details about the sender of the message.\n       * @param {function(*)} sendResponse\n       *        A callback which, when called with an arbitrary argument, sends\n       *        that value as a response.\n       * @returns {boolean}\n       *        True if the wrapped listener returned a Promise, which will later\n       *        yield a response. False otherwise.\n       */\n      return function onMessage(message, sender, sendResponse) {\n        let didCallSendResponse = false;\n\n        let wrappedSendResponse;\n        let sendResponsePromise = new Promise(resolve => {\n          wrappedSendResponse = function(response) {\n            didCallSendResponse = true;\n            resolve(response);\n          };\n        });\n\n        let result;\n        try {\n          result = listener(message, sender, wrappedSendResponse);\n        } catch (err) {\n          result = Promise.reject(err);\n        }\n\n        const isResultThenable = result !== true && isThenable(result);\n\n        // If the listener didn't returned true or a Promise, or called\n        // wrappedSendResponse synchronously, we can exit earlier\n        // because there will be no response sent from this listener.\n        if (result !== true && !isResultThenable && !didCallSendResponse) {\n          return false;\n        }\n\n        // A small helper to send the message if the promise resolves\n        // and an error if the promise rejects (a wrapped sendMessage has\n        // to translate the message into a resolved promise or a rejected\n        // promise).\n        const sendPromisedResult = (promise) => {\n          promise.then(msg => {\n            // send the message value.\n            sendResponse(msg);\n          }, error => {\n            // Send a JSON representation of the error if the rejected value\n            // is an instance of error, or the object itself otherwise.\n            let message;\n            if (error && (error instanceof Error ||\n                typeof error.message === \"string\")) {\n              message = error.message;\n            } else {\n              message = \"An unexpected error occurred\";\n            }\n\n            sendResponse({\n              __mozWebExtensionPolyfillReject__: true,\n              message,\n            });\n          }).catch(err => {\n            // Print an error on the console if unable to send the response.\n            console.error(\"Failed to send onMessage rejected reply\", err);\n          });\n        };\n\n        // If the listener returned a Promise, send the resolved value as a\n        // result, otherwise wait the promise related to the wrappedSendResponse\n        // callback to resolve and send it as a response.\n        if (isResultThenable) {\n          sendPromisedResult(result);\n        } else {\n          sendPromisedResult(sendResponsePromise);\n        }\n\n        // Let Chrome know that the listener is replying.\n        return true;\n      };\n    });\n\n    const wrappedSendMessageCallback = ({reject, resolve}, reply) => {\n      if (extensionAPIs.runtime.lastError) {\n        // Detect when none of the listeners replied to the sendMessage call and resolve\n        // the promise to undefined as in Firefox.\n        // See https://github.com/mozilla/webextension-polyfill/issues/130\n        if (extensionAPIs.runtime.lastError.message === CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE) {\n          resolve();\n        } else {\n          reject(new Error(extensionAPIs.runtime.lastError.message));\n        }\n      } else if (reply && reply.__mozWebExtensionPolyfillReject__) {\n        // Convert back the JSON representation of the error into\n        // an Error instance.\n        reject(new Error(reply.message));\n      } else {\n        resolve(reply);\n      }\n    };\n\n    const wrappedSendMessage = (name, metadata, apiNamespaceObj, ...args) => {\n      if (args.length < metadata.minArgs) {\n        throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n      }\n\n      if (args.length > metadata.maxArgs) {\n        throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n      }\n\n      return new Promise((resolve, reject) => {\n        const wrappedCb = wrappedSendMessageCallback.bind(null, {resolve, reject});\n        args.push(wrappedCb);\n        apiNamespaceObj.sendMessage(...args);\n      });\n    };\n\n    const staticWrappers = {\n      devtools: {\n        network: {\n          onRequestFinished: wrapEvent(onRequestFinishedWrappers),\n        },\n      },\n      runtime: {\n        onMessage: wrapEvent(onMessageWrappers),\n        onMessageExternal: wrapEvent(onMessageWrappers),\n        sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {minArgs: 1, maxArgs: 3}),\n      },\n      tabs: {\n        sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {minArgs: 2, maxArgs: 3}),\n      },\n    };\n    const settingMetadata = {\n      clear: {minArgs: 1, maxArgs: 1},\n      get: {minArgs: 1, maxArgs: 1},\n      set: {minArgs: 1, maxArgs: 1},\n    };\n    apiMetadata.privacy = {\n      network: {\"*\": settingMetadata},\n      services: {\"*\": settingMetadata},\n      websites: {\"*\": settingMetadata},\n    };\n\n    return wrapObject(extensionAPIs, staticWrappers, apiMetadata);\n  };\n\n  // The build process adds a UMD wrapper around this file, which makes the\n  // `module` variable available.\n  module.exports = wrapAPIs(chrome);\n} else {\n  module.exports = globalThis.browser;\n}\n"]}
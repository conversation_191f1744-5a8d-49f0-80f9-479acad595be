{"version": 3, "file": "browser-polyfill.js", "names": ["globalThis", "chrome", "runtime", "id", "Error", "browser", "CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE", "wrapAPIs", "extensionAPIs", "apiMetadata", "Object", "keys", "length", "DefaultWeakMap", "WeakMap", "constructor", "createItem", "items", "undefined", "get", "key", "has", "set", "isThenable", "value", "then", "makeCallback", "promise", "metadata", "callback<PERSON><PERSON><PERSON>", "lastError", "reject", "message", "singleCallbackArg", "resolve", "pluralizeArguments", "numArgs", "wrapAsyncFunction", "name", "asyncFunctionWrapper", "target", "args", "min<PERSON><PERSON>s", "maxArgs", "Promise", "fallback<PERSON><PERSON><PERSON><PERSON>Call<PERSON>", "cb<PERSON><PERSON>r", "console", "warn", "noCallback", "wrapMethod", "method", "wrapper", "Proxy", "apply", "targetMethod", "thisObj", "call", "hasOwnProperty", "Function", "bind", "prototype", "wrapObject", "wrappers", "cache", "create", "handlers", "proxyTarget", "prop", "receiver", "defineProperty", "configurable", "enumerable", "desc", "Reflect", "deleteProperty", "wrapEvent", "wrapperMap", "addListener", "listener", "hasListener", "removeListener", "onRequestFinishedWrappers", "onRequestFinished", "req", "wrappedReq", "get<PERSON>ontent", "onMessageWrappers", "onMessage", "sender", "sendResponse", "didCallSendResponse", "wrappedSendResponse", "sendResponsePromise", "response", "result", "err", "isResultThenable", "sendPromisedResult", "msg", "error", "__mozWebExtensionPolyfillReject__", "catch", "wrappedSendMessageCallback", "reply", "wrappedSendMessage", "apiNamespaceObj", "wrappedCb", "push", "sendMessage", "staticWrappers", "devtools", "network", "onMessageExternal", "tabs", "settingMetadata", "clear", "privacy", "services", "websites", "module", "exports"], "sources": ["browser-polyfill.js"], "sourcesContent": ["/* webextension-polyfill - v0.12.0 - <PERSON>e May 14 2024 18:01:29 */\n/* -*- Mode: indent-tabs-mode: nil; js-indent-level: 2 -*- */\n/* vim: set sts=2 sw=2 et tw=80: */\n/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at http://mozilla.org/MPL/2.0/. */\n\"use strict\";\n\nif (!(globalThis.chrome && globalThis.chrome.runtime && globalThis.chrome.runtime.id)) {\n  throw new Error(\"This script should only be loaded in a browser extension.\");\n}\n\nif (!(globalThis.browser && globalThis.browser.runtime && globalThis.browser.runtime.id)) {\n  const CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE = \"The message port closed before a response was received.\";\n\n  // Wrapping the bulk of this polyfill in a one-time-use function is a minor\n  // optimization for Firefox. Since Spidermonkey does not fully parse the\n  // contents of a function until the first time it's called, and since it will\n  // never actually need to be called, this allows the polyfill to be included\n  // in Firefox nearly for free.\n  const wrapAPIs = extensionAPIs => {\n    // NOTE: apiMetadata is associated to the content of the api-metadata.json file\n    // at build time by replacing the following \"include\" with the content of the\n    // JSON file.\n    const apiMetadata = {\n      \"alarms\": {\n        \"clear\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"clearAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"get\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"bookmarks\": {\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getChildren\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getRecent\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getSubTree\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTree\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"move\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeTree\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"browserAction\": {\n        \"disable\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"enable\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"getBadgeBackgroundColor\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getBadgeText\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"openPopup\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"setBadgeBackgroundColor\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setBadgeText\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"setPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"browsingData\": {\n        \"remove\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"removeCache\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeCookies\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeDownloads\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeFormData\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeHistory\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeLocalStorage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removePasswords\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removePluginData\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"settings\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"commands\": {\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"contextMenus\": {\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"cookies\": {\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAllCookieStores\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"set\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"devtools\": {\n        \"inspectedWindow\": {\n          \"eval\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2,\n            \"singleCallbackArg\": false\n          }\n        },\n        \"panels\": {\n          \"create\": {\n            \"minArgs\": 3,\n            \"maxArgs\": 3,\n            \"singleCallbackArg\": true\n          },\n          \"elements\": {\n            \"createSidebarPane\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 1\n            }\n          }\n        }\n      },\n      \"downloads\": {\n        \"cancel\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"download\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"erase\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getFileIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"open\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"pause\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeFile\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"resume\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"show\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"extension\": {\n        \"isAllowedFileSchemeAccess\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"isAllowedIncognitoAccess\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"history\": {\n        \"addUrl\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"deleteAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"deleteRange\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"deleteUrl\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getVisits\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"i18n\": {\n        \"detectLanguage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAcceptLanguages\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"identity\": {\n        \"launchWebAuthFlow\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"idle\": {\n        \"queryState\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"management\": {\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getSelf\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"setEnabled\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"uninstallSelf\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        }\n      },\n      \"notifications\": {\n        \"clear\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getPermissionLevel\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"pageAction\": {\n        \"getPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"hide\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"setPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"show\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"permissions\": {\n        \"contains\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"request\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"runtime\": {\n        \"getBackgroundPage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getPlatformInfo\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"openOptionsPage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"requestUpdateCheck\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"sendMessage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 3\n        },\n        \"sendNativeMessage\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"setUninstallURL\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"sessions\": {\n        \"getDevices\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getRecentlyClosed\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"restore\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        }\n      },\n      \"storage\": {\n        \"local\": {\n          \"clear\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"set\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"managed\": {\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          }\n        },\n        \"sync\": {\n          \"clear\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"set\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        }\n      },\n      \"tabs\": {\n        \"captureVisibleTab\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 2\n        },\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"detectLanguage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"discard\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"duplicate\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"executeScript\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getCurrent\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getZoom\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getZoomSettings\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"goBack\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"goForward\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"highlight\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"insertCSS\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"move\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"query\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"reload\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 2\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeCSS\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"sendMessage\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 3\n        },\n        \"setZoom\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"setZoomSettings\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"update\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        }\n      },\n      \"topSites\": {\n        \"get\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"webNavigation\": {\n        \"getAllFrames\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getFrame\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"webRequest\": {\n        \"handlerBehaviorChanged\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"windows\": {\n        \"create\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getCurrent\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getLastFocused\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      }\n    };\n\n    if (Object.keys(apiMetadata).length === 0) {\n      throw new Error(\"api-metadata.json has not been included in browser-polyfill\");\n    }\n\n    /**\n     * A WeakMap subclass which creates and stores a value for any key which does\n     * not exist when accessed, but behaves exactly as an ordinary WeakMap\n     * otherwise.\n     *\n     * @param {function} createItem\n     *        A function which will be called in order to create the value for any\n     *        key which does not exist, the first time it is accessed. The\n     *        function receives, as its only argument, the key being created.\n     */\n    class DefaultWeakMap extends WeakMap {\n      constructor(createItem, items = undefined) {\n        super(items);\n        this.createItem = createItem;\n      }\n\n      get(key) {\n        if (!this.has(key)) {\n          this.set(key, this.createItem(key));\n        }\n\n        return super.get(key);\n      }\n    }\n\n    /**\n     * Returns true if the given object is an object with a `then` method, and can\n     * therefore be assumed to behave as a Promise.\n     *\n     * @param {*} value The value to test.\n     * @returns {boolean} True if the value is thenable.\n     */\n    const isThenable = value => {\n      return value && typeof value === \"object\" && typeof value.then === \"function\";\n    };\n\n    /**\n     * Creates and returns a function which, when called, will resolve or reject\n     * the given promise based on how it is called:\n     *\n     * - If, when called, `chrome.runtime.lastError` contains a non-null object,\n     *   the promise is rejected with that value.\n     * - If the function is called with exactly one argument, the promise is\n     *   resolved to that value.\n     * - Otherwise, the promise is resolved to an array containing all of the\n     *   function's arguments.\n     *\n     * @param {object} promise\n     *        An object containing the resolution and rejection functions of a\n     *        promise.\n     * @param {function} promise.resolve\n     *        The promise's resolution function.\n     * @param {function} promise.reject\n     *        The promise's rejection function.\n     * @param {object} metadata\n     *        Metadata about the wrapped method which has created the callback.\n     * @param {boolean} metadata.singleCallbackArg\n     *        Whether or not the promise is resolved with only the first\n     *        argument of the callback, alternatively an array of all the\n     *        callback arguments is resolved. By default, if the callback\n     *        function is invoked with only a single argument, that will be\n     *        resolved to the promise, while all arguments will be resolved as\n     *        an array if multiple are given.\n     *\n     * @returns {function}\n     *        The generated callback function.\n     */\n    const makeCallback = (promise, metadata) => {\n      return (...callbackArgs) => {\n        if (extensionAPIs.runtime.lastError) {\n          promise.reject(new Error(extensionAPIs.runtime.lastError.message));\n        } else if (metadata.singleCallbackArg ||\n                   (callbackArgs.length <= 1 && metadata.singleCallbackArg !== false)) {\n          promise.resolve(callbackArgs[0]);\n        } else {\n          promise.resolve(callbackArgs);\n        }\n      };\n    };\n\n    const pluralizeArguments = (numArgs) => numArgs == 1 ? \"argument\" : \"arguments\";\n\n    /**\n     * Creates a wrapper function for a method with the given name and metadata.\n     *\n     * @param {string} name\n     *        The name of the method which is being wrapped.\n     * @param {object} metadata\n     *        Metadata about the method being wrapped.\n     * @param {integer} metadata.minArgs\n     *        The minimum number of arguments which must be passed to the\n     *        function. If called with fewer than this number of arguments, the\n     *        wrapper will raise an exception.\n     * @param {integer} metadata.maxArgs\n     *        The maximum number of arguments which may be passed to the\n     *        function. If called with more than this number of arguments, the\n     *        wrapper will raise an exception.\n     * @param {boolean} metadata.singleCallbackArg\n     *        Whether or not the promise is resolved with only the first\n     *        argument of the callback, alternatively an array of all the\n     *        callback arguments is resolved. By default, if the callback\n     *        function is invoked with only a single argument, that will be\n     *        resolved to the promise, while all arguments will be resolved as\n     *        an array if multiple are given.\n     *\n     * @returns {function(object, ...*)}\n     *       The generated wrapper function.\n     */\n    const wrapAsyncFunction = (name, metadata) => {\n      return function asyncFunctionWrapper(target, ...args) {\n        if (args.length < metadata.minArgs) {\n          throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        if (args.length > metadata.maxArgs) {\n          throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        return new Promise((resolve, reject) => {\n          if (metadata.fallbackToNoCallback) {\n            // This API method has currently no callback on Chrome, but it return a promise on Firefox,\n            // and so the polyfill will try to call it with a callback first, and it will fallback\n            // to not passing the callback if the first call fails.\n            try {\n              target[name](...args, makeCallback({resolve, reject}, metadata));\n            } catch (cbError) {\n              console.warn(`${name} API method doesn't seem to support the callback parameter, ` +\n                           \"falling back to call it without a callback: \", cbError);\n\n              target[name](...args);\n\n              // Update the API method metadata, so that the next API calls will not try to\n              // use the unsupported callback anymore.\n              metadata.fallbackToNoCallback = false;\n              metadata.noCallback = true;\n\n              resolve();\n            }\n          } else if (metadata.noCallback) {\n            target[name](...args);\n            resolve();\n          } else {\n            target[name](...args, makeCallback({resolve, reject}, metadata));\n          }\n        });\n      };\n    };\n\n    /**\n     * Wraps an existing method of the target object, so that calls to it are\n     * intercepted by the given wrapper function. The wrapper function receives,\n     * as its first argument, the original `target` object, followed by each of\n     * the arguments passed to the original method.\n     *\n     * @param {object} target\n     *        The original target object that the wrapped method belongs to.\n     * @param {function} method\n     *        The method being wrapped. This is used as the target of the Proxy\n     *        object which is created to wrap the method.\n     * @param {function} wrapper\n     *        The wrapper function which is called in place of a direct invocation\n     *        of the wrapped method.\n     *\n     * @returns {Proxy<function>}\n     *        A Proxy object for the given method, which invokes the given wrapper\n     *        method in its place.\n     */\n    const wrapMethod = (target, method, wrapper) => {\n      return new Proxy(method, {\n        apply(targetMethod, thisObj, args) {\n          return wrapper.call(thisObj, target, ...args);\n        },\n      });\n    };\n\n    let hasOwnProperty = Function.call.bind(Object.prototype.hasOwnProperty);\n\n    /**\n     * Wraps an object in a Proxy which intercepts and wraps certain methods\n     * based on the given `wrappers` and `metadata` objects.\n     *\n     * @param {object} target\n     *        The target object to wrap.\n     *\n     * @param {object} [wrappers = {}]\n     *        An object tree containing wrapper functions for special cases. Any\n     *        function present in this object tree is called in place of the\n     *        method in the same location in the `target` object tree. These\n     *        wrapper methods are invoked as described in {@see wrapMethod}.\n     *\n     * @param {object} [metadata = {}]\n     *        An object tree containing metadata used to automatically generate\n     *        Promise-based wrapper functions for asynchronous. Any function in\n     *        the `target` object tree which has a corresponding metadata object\n     *        in the same location in the `metadata` tree is replaced with an\n     *        automatically-generated wrapper function, as described in\n     *        {@see wrapAsyncFunction}\n     *\n     * @returns {Proxy<object>}\n     */\n    const wrapObject = (target, wrappers = {}, metadata = {}) => {\n      let cache = Object.create(null);\n      let handlers = {\n        has(proxyTarget, prop) {\n          return prop in target || prop in cache;\n        },\n\n        get(proxyTarget, prop, receiver) {\n          if (prop in cache) {\n            return cache[prop];\n          }\n\n          if (!(prop in target)) {\n            return undefined;\n          }\n\n          let value = target[prop];\n\n          if (typeof value === \"function\") {\n            // This is a method on the underlying object. Check if we need to do\n            // any wrapping.\n\n            if (typeof wrappers[prop] === \"function\") {\n              // We have a special-case wrapper for this method.\n              value = wrapMethod(target, target[prop], wrappers[prop]);\n            } else if (hasOwnProperty(metadata, prop)) {\n              // This is an async method that we have metadata for. Create a\n              // Promise wrapper for it.\n              let wrapper = wrapAsyncFunction(prop, metadata[prop]);\n              value = wrapMethod(target, target[prop], wrapper);\n            } else {\n              // This is a method that we don't know or care about. Return the\n              // original method, bound to the underlying object.\n              value = value.bind(target);\n            }\n          } else if (typeof value === \"object\" && value !== null &&\n                     (hasOwnProperty(wrappers, prop) ||\n                      hasOwnProperty(metadata, prop))) {\n            // This is an object that we need to do some wrapping for the children\n            // of. Create a sub-object wrapper for it with the appropriate child\n            // metadata.\n            value = wrapObject(value, wrappers[prop], metadata[prop]);\n          } else if (hasOwnProperty(metadata, \"*\")) {\n            // Wrap all properties in * namespace.\n            value = wrapObject(value, wrappers[prop], metadata[\"*\"]);\n          } else {\n            // We don't need to do any wrapping for this property,\n            // so just forward all access to the underlying object.\n            Object.defineProperty(cache, prop, {\n              configurable: true,\n              enumerable: true,\n              get() {\n                return target[prop];\n              },\n              set(value) {\n                target[prop] = value;\n              },\n            });\n\n            return value;\n          }\n\n          cache[prop] = value;\n          return value;\n        },\n\n        set(proxyTarget, prop, value, receiver) {\n          if (prop in cache) {\n            cache[prop] = value;\n          } else {\n            target[prop] = value;\n          }\n          return true;\n        },\n\n        defineProperty(proxyTarget, prop, desc) {\n          return Reflect.defineProperty(cache, prop, desc);\n        },\n\n        deleteProperty(proxyTarget, prop) {\n          return Reflect.deleteProperty(cache, prop);\n        },\n      };\n\n      // Per contract of the Proxy API, the \"get\" proxy handler must return the\n      // original value of the target if that value is declared read-only and\n      // non-configurable. For this reason, we create an object with the\n      // prototype set to `target` instead of using `target` directly.\n      // Otherwise we cannot return a custom object for APIs that\n      // are declared read-only and non-configurable, such as `chrome.devtools`.\n      //\n      // The proxy handlers themselves will still use the original `target`\n      // instead of the `proxyTarget`, so that the methods and properties are\n      // dereferenced via the original targets.\n      let proxyTarget = Object.create(target);\n      return new Proxy(proxyTarget, handlers);\n    };\n\n    /**\n     * Creates a set of wrapper functions for an event object, which handles\n     * wrapping of listener functions that those messages are passed.\n     *\n     * A single wrapper is created for each listener function, and stored in a\n     * map. Subsequent calls to `addListener`, `hasListener`, or `removeListener`\n     * retrieve the original wrapper, so that  attempts to remove a\n     * previously-added listener work as expected.\n     *\n     * @param {DefaultWeakMap<function, function>} wrapperMap\n     *        A DefaultWeakMap object which will create the appropriate wrapper\n     *        for a given listener function when one does not exist, and retrieve\n     *        an existing one when it does.\n     *\n     * @returns {object}\n     */\n    const wrapEvent = wrapperMap => ({\n      addListener(target, listener, ...args) {\n        target.addListener(wrapperMap.get(listener), ...args);\n      },\n\n      hasListener(target, listener) {\n        return target.hasListener(wrapperMap.get(listener));\n      },\n\n      removeListener(target, listener) {\n        target.removeListener(wrapperMap.get(listener));\n      },\n    });\n\n    const onRequestFinishedWrappers = new DefaultWeakMap(listener => {\n      if (typeof listener !== \"function\") {\n        return listener;\n      }\n\n      /**\n       * Wraps an onRequestFinished listener function so that it will return a\n       * `getContent()` property which returns a `Promise` rather than using a\n       * callback API.\n       *\n       * @param {object} req\n       *        The HAR entry object representing the network request.\n       */\n      return function onRequestFinished(req) {\n        const wrappedReq = wrapObject(req, {} /* wrappers */, {\n          getContent: {\n            minArgs: 0,\n            maxArgs: 0,\n          },\n        });\n        listener(wrappedReq);\n      };\n    });\n\n    const onMessageWrappers = new DefaultWeakMap(listener => {\n      if (typeof listener !== \"function\") {\n        return listener;\n      }\n\n      /**\n       * Wraps a message listener function so that it may send responses based on\n       * its return value, rather than by returning a sentinel value and calling a\n       * callback. If the listener function returns a Promise, the response is\n       * sent when the promise either resolves or rejects.\n       *\n       * @param {*} message\n       *        The message sent by the other end of the channel.\n       * @param {object} sender\n       *        Details about the sender of the message.\n       * @param {function(*)} sendResponse\n       *        A callback which, when called with an arbitrary argument, sends\n       *        that value as a response.\n       * @returns {boolean}\n       *        True if the wrapped listener returned a Promise, which will later\n       *        yield a response. False otherwise.\n       */\n      return function onMessage(message, sender, sendResponse) {\n        let didCallSendResponse = false;\n\n        let wrappedSendResponse;\n        let sendResponsePromise = new Promise(resolve => {\n          wrappedSendResponse = function(response) {\n            didCallSendResponse = true;\n            resolve(response);\n          };\n        });\n\n        let result;\n        try {\n          result = listener(message, sender, wrappedSendResponse);\n        } catch (err) {\n          result = Promise.reject(err);\n        }\n\n        const isResultThenable = result !== true && isThenable(result);\n\n        // If the listener didn't returned true or a Promise, or called\n        // wrappedSendResponse synchronously, we can exit earlier\n        // because there will be no response sent from this listener.\n        if (result !== true && !isResultThenable && !didCallSendResponse) {\n          return false;\n        }\n\n        // A small helper to send the message if the promise resolves\n        // and an error if the promise rejects (a wrapped sendMessage has\n        // to translate the message into a resolved promise or a rejected\n        // promise).\n        const sendPromisedResult = (promise) => {\n          promise.then(msg => {\n            // send the message value.\n            sendResponse(msg);\n          }, error => {\n            // Send a JSON representation of the error if the rejected value\n            // is an instance of error, or the object itself otherwise.\n            let message;\n            if (error && (error instanceof Error ||\n                typeof error.message === \"string\")) {\n              message = error.message;\n            } else {\n              message = \"An unexpected error occurred\";\n            }\n\n            sendResponse({\n              __mozWebExtensionPolyfillReject__: true,\n              message,\n            });\n          }).catch(err => {\n            // Print an error on the console if unable to send the response.\n            console.error(\"Failed to send onMessage rejected reply\", err);\n          });\n        };\n\n        // If the listener returned a Promise, send the resolved value as a\n        // result, otherwise wait the promise related to the wrappedSendResponse\n        // callback to resolve and send it as a response.\n        if (isResultThenable) {\n          sendPromisedResult(result);\n        } else {\n          sendPromisedResult(sendResponsePromise);\n        }\n\n        // Let Chrome know that the listener is replying.\n        return true;\n      };\n    });\n\n    const wrappedSendMessageCallback = ({reject, resolve}, reply) => {\n      if (extensionAPIs.runtime.lastError) {\n        // Detect when none of the listeners replied to the sendMessage call and resolve\n        // the promise to undefined as in Firefox.\n        // See https://github.com/mozilla/webextension-polyfill/issues/130\n        if (extensionAPIs.runtime.lastError.message === CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE) {\n          resolve();\n        } else {\n          reject(new Error(extensionAPIs.runtime.lastError.message));\n        }\n      } else if (reply && reply.__mozWebExtensionPolyfillReject__) {\n        // Convert back the JSON representation of the error into\n        // an Error instance.\n        reject(new Error(reply.message));\n      } else {\n        resolve(reply);\n      }\n    };\n\n    const wrappedSendMessage = (name, metadata, apiNamespaceObj, ...args) => {\n      if (args.length < metadata.minArgs) {\n        throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n      }\n\n      if (args.length > metadata.maxArgs) {\n        throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n      }\n\n      return new Promise((resolve, reject) => {\n        const wrappedCb = wrappedSendMessageCallback.bind(null, {resolve, reject});\n        args.push(wrappedCb);\n        apiNamespaceObj.sendMessage(...args);\n      });\n    };\n\n    const staticWrappers = {\n      devtools: {\n        network: {\n          onRequestFinished: wrapEvent(onRequestFinishedWrappers),\n        },\n      },\n      runtime: {\n        onMessage: wrapEvent(onMessageWrappers),\n        onMessageExternal: wrapEvent(onMessageWrappers),\n        sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {minArgs: 1, maxArgs: 3}),\n      },\n      tabs: {\n        sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {minArgs: 2, maxArgs: 3}),\n      },\n    };\n    const settingMetadata = {\n      clear: {minArgs: 1, maxArgs: 1},\n      get: {minArgs: 1, maxArgs: 1},\n      set: {minArgs: 1, maxArgs: 1},\n    };\n    apiMetadata.privacy = {\n      network: {\"*\": settingMetadata},\n      services: {\"*\": settingMetadata},\n      websites: {\"*\": settingMetadata},\n    };\n\n    return wrapObject(extensionAPIs, staticWrappers, apiMetadata);\n  };\n\n  // The build process adds a UMD wrapper around this file, which makes the\n  // `module` variable available.\n  module.exports = wrapAPIs(chrome);\n} else {\n  module.exports = globalThis.browser;\n}\n"], "mappings": ";;;;;;;;;;;;;EAAA;EACA;EACA;EACA;AACA;AACA;EACA,YAAY;;EAEZ,IAAI,EAAEA,UAAU,CAACC,MAAM,IAAID,UAAU,CAACC,MAAM,CAACC,OAAO,IAAIF,UAAU,CAACC,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,EAAE;IACrF,MAAM,IAAIC,KAAK,CAAC,2DAA2D,CAAC;EAC9E;EAEA,IAAI,EAAEJ,UAAU,CAACK,OAAO,IAAIL,UAAU,CAACK,OAAO,CAACH,OAAO,IAAIF,UAAU,CAACK,OAAO,CAACH,OAAO,CAACC,EAAE,CAAC,EAAE;IACxF,MAAMG,gDAAgD,GAAG,yDAAyD;;IAElH;IACA;IACA;IACA;IACA;IACA,MAAMC,QAAQ,GAAGC,aAAa,IAAI;MAChC;MACA;MACA;MACA,MAAMC,WAAW,GAAG;QAClB,QAAQ,EAAE;UACR,OAAO,EAAE;YACP,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,WAAW,EAAE;UACX,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,aAAa,EAAE;YACb,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,MAAM,EAAE;YACN,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,eAAe,EAAE;UACf,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,yBAAyB,EAAE;YACzB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,cAAc,EAAE;YACd,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,yBAAyB,EAAE;YACzB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,cAAc,EAAE;YACd,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B;QACF,CAAC;QACD,cAAc,EAAE;UACd,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,aAAa,EAAE;YACb,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,eAAe,EAAE;YACf,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,gBAAgB,EAAE;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,eAAe,EAAE;YACf,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,oBAAoB,EAAE;YACpB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,kBAAkB,EAAE;YAClB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,UAAU,EAAE;UACV,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,cAAc,EAAE;UACd,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,SAAS,EAAE;UACT,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,oBAAoB,EAAE;YACpB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,UAAU,EAAE;UACV,iBAAiB,EAAE;YACjB,MAAM,EAAE;cACN,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE,CAAC;cACZ,mBAAmB,EAAE;YACvB;UACF,CAAC;UACD,QAAQ,EAAE;YACR,QAAQ,EAAE;cACR,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE,CAAC;cACZ,mBAAmB,EAAE;YACvB,CAAC;YACD,UAAU,EAAE;cACV,mBAAmB,EAAE;gBACnB,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE;cACb;YACF;UACF;QACF,CAAC;QACD,WAAW,EAAE;UACX,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,OAAO,EAAE;YACP,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,aAAa,EAAE;YACb,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,MAAM,EAAE;YACN,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,OAAO,EAAE;YACP,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,MAAM,EAAE;YACN,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B;QACF,CAAC;QACD,WAAW,EAAE;UACX,2BAA2B,EAAE;YAC3B,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,0BAA0B,EAAE;YAC1B,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,SAAS,EAAE;UACT,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,aAAa,EAAE;YACb,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,MAAM,EAAE;UACN,gBAAgB,EAAE;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,oBAAoB,EAAE;YACpB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,UAAU,EAAE;UACV,mBAAmB,EAAE;YACnB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,MAAM,EAAE;UACN,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,YAAY,EAAE;UACZ,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,eAAe,EAAE;YACf,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,eAAe,EAAE;UACf,OAAO,EAAE;YACP,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,oBAAoB,EAAE;YACpB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,YAAY,EAAE;UACZ,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,MAAM,EAAE;YACN,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B,CAAC;UACD,MAAM,EAAE;YACN,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,sBAAsB,EAAE;UAC1B;QACF,CAAC;QACD,aAAa,EAAE;UACb,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,SAAS,EAAE;UACT,mBAAmB,EAAE;YACnB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,oBAAoB,EAAE;YACpB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,aAAa,EAAE;YACb,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,mBAAmB,EAAE;YACnB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,UAAU,EAAE;UACV,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,mBAAmB,EAAE;YACnB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,SAAS,EAAE;UACT,OAAO,EAAE;YACP,OAAO,EAAE;cACP,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,KAAK,EAAE;cACL,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,eAAe,EAAE;cACf,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,QAAQ,EAAE;cACR,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,KAAK,EAAE;cACL,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb;UACF,CAAC;UACD,SAAS,EAAE;YACT,KAAK,EAAE;cACL,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,eAAe,EAAE;cACf,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb;UACF,CAAC;UACD,MAAM,EAAE;YACN,OAAO,EAAE;cACP,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,KAAK,EAAE;cACL,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,eAAe,EAAE;cACf,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,QAAQ,EAAE;cACR,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb,CAAC;YACD,KAAK,EAAE;cACL,SAAS,EAAE,CAAC;cACZ,SAAS,EAAE;YACb;UACF;QACF,CAAC;QACD,MAAM,EAAE;UACN,mBAAmB,EAAE;YACnB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,gBAAgB,EAAE;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,eAAe,EAAE;YACf,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,MAAM,EAAE;YACN,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,OAAO,EAAE;YACP,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,aAAa,EAAE;YACb,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,SAAS,EAAE;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,UAAU,EAAE;UACV,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,eAAe,EAAE;UACf,cAAc,EAAE;YACd,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,YAAY,EAAE;UACZ,wBAAwB,EAAE;YACxB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF,CAAC;QACD,SAAS,EAAE;UACT,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,YAAY,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,gBAAgB,EAAE;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb,CAAC;UACD,QAAQ,EAAE;YACR,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE;UACb;QACF;MACF,CAAC;MAED,IAAIC,MAAM,CAACC,IAAI,CAACF,WAAW,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;QACzC,MAAM,IAAIR,KAAK,CAAC,6DAA6D,CAAC;MAChF;;MAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAMS,cAAc,SAASC,OAAO,CAAC;QACnCC,WAAWA,CAACC,UAAU,EAAEC,KAAK,GAAGC,SAAS,EAAE;UACzC,KAAK,CAACD,KAAK,CAAC;UACZ,IAAI,CAACD,UAAU,GAAGA,UAAU;QAC9B;QAEAG,GAAGA,CAACC,GAAG,EAAE;UACP,IAAI,CAAC,IAAI,CAACC,GAAG,CAACD,GAAG,CAAC,EAAE;YAClB,IAAI,CAACE,GAAG,CAACF,GAAG,EAAE,IAAI,CAACJ,UAAU,CAACI,GAAG,CAAC,CAAC;UACrC;UAEA,OAAO,KAAK,CAACD,GAAG,CAACC,GAAG,CAAC;QACvB;MACF;;MAEA;AACJ;AACA;AACA;AACA;AACA;AACA;MACI,MAAMG,UAAU,GAAGC,KAAK,IAAI;QAC1B,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU;MAC/E,CAAC;;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAMC,YAAY,GAAGA,CAACC,OAAO,EAAEC,QAAQ,KAAK;QAC1C,OAAO,CAAC,GAAGC,YAAY,KAAK;UAC1B,IAAIrB,aAAa,CAACN,OAAO,CAAC4B,SAAS,EAAE;YACnCH,OAAO,CAACI,MAAM,CAAC,IAAI3B,KAAK,CAACI,aAAa,CAACN,OAAO,CAAC4B,SAAS,CAACE,OAAO,CAAC,CAAC;UACpE,CAAC,MAAM,IAAIJ,QAAQ,CAACK,iBAAiB,IACzBJ,YAAY,CAACjB,MAAM,IAAI,CAAC,IAAIgB,QAAQ,CAACK,iBAAiB,KAAK,KAAM,EAAE;YAC7EN,OAAO,CAACO,OAAO,CAACL,YAAY,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,MAAM;YACLF,OAAO,CAACO,OAAO,CAACL,YAAY,CAAC;UAC/B;QACF,CAAC;MACH,CAAC;MAED,MAAMM,kBAAkB,GAAIC,OAAO,IAAKA,OAAO,IAAI,CAAC,GAAG,UAAU,GAAG,WAAW;;MAE/E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAMC,iBAAiB,GAAGA,CAACC,IAAI,EAAEV,QAAQ,KAAK;QAC5C,OAAO,SAASW,oBAAoBA,CAACC,MAAM,EAAE,GAAGC,IAAI,EAAE;UACpD,IAAIA,IAAI,CAAC7B,MAAM,GAAGgB,QAAQ,CAACc,OAAO,EAAE;YAClC,MAAM,IAAItC,KAAK,CAAE,qBAAoBwB,QAAQ,CAACc,OAAQ,IAAGP,kBAAkB,CAACP,QAAQ,CAACc,OAAO,CAAE,QAAOJ,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAAC,CAAC;UACpI;UAEA,IAAI6B,IAAI,CAAC7B,MAAM,GAAGgB,QAAQ,CAACe,OAAO,EAAE;YAClC,MAAM,IAAIvC,KAAK,CAAE,oBAAmBwB,QAAQ,CAACe,OAAQ,IAAGR,kBAAkB,CAACP,QAAQ,CAACe,OAAO,CAAE,QAAOL,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAAC,CAAC;UACnI;UAEA,OAAO,IAAIgC,OAAO,CAAC,CAACV,OAAO,EAAEH,MAAM,KAAK;YACtC,IAAIH,QAAQ,CAACiB,oBAAoB,EAAE;cACjC;cACA;cACA;cACA,IAAI;gBACFL,MAAM,CAACF,IAAI,CAAC,CAAC,GAAGG,IAAI,EAAEf,YAAY,CAAC;kBAACQ,OAAO;kBAAEH;gBAAM,CAAC,EAAEH,QAAQ,CAAC,CAAC;cAClE,CAAC,CAAC,OAAOkB,OAAO,EAAE;gBAChBC,OAAO,CAACC,IAAI,CAAE,GAAEV,IAAK,8DAA6D,GACrE,8CAA8C,EAAEQ,OAAO,CAAC;gBAErEN,MAAM,CAACF,IAAI,CAAC,CAAC,GAAGG,IAAI,CAAC;;gBAErB;gBACA;gBACAb,QAAQ,CAACiB,oBAAoB,GAAG,KAAK;gBACrCjB,QAAQ,CAACqB,UAAU,GAAG,IAAI;gBAE1Bf,OAAO,CAAC,CAAC;cACX;YACF,CAAC,MAAM,IAAIN,QAAQ,CAACqB,UAAU,EAAE;cAC9BT,MAAM,CAACF,IAAI,CAAC,CAAC,GAAGG,IAAI,CAAC;cACrBP,OAAO,CAAC,CAAC;YACX,CAAC,MAAM;cACLM,MAAM,CAACF,IAAI,CAAC,CAAC,GAAGG,IAAI,EAAEf,YAAY,CAAC;gBAACQ,OAAO;gBAAEH;cAAM,CAAC,EAAEH,QAAQ,CAAC,CAAC;YAClE;UACF,CAAC,CAAC;QACJ,CAAC;MACH,CAAC;;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAMsB,UAAU,GAAGA,CAACV,MAAM,EAAEW,MAAM,EAAEC,OAAO,KAAK;QAC9C,OAAO,IAAIC,KAAK,CAACF,MAAM,EAAE;UACvBG,KAAKA,CAACC,YAAY,EAAEC,OAAO,EAAEf,IAAI,EAAE;YACjC,OAAOW,OAAO,CAACK,IAAI,CAACD,OAAO,EAAEhB,MAAM,EAAE,GAAGC,IAAI,CAAC;UAC/C;QACF,CAAC,CAAC;MACJ,CAAC;MAED,IAAIiB,cAAc,GAAGC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAClD,MAAM,CAACmD,SAAS,CAACH,cAAc,CAAC;;MAExE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAMI,UAAU,GAAGA,CAACtB,MAAM,EAAEuB,QAAQ,GAAG,CAAC,CAAC,EAAEnC,QAAQ,GAAG,CAAC,CAAC,KAAK;QAC3D,IAAIoC,KAAK,GAAGtD,MAAM,CAACuD,MAAM,CAAC,IAAI,CAAC;QAC/B,IAAIC,QAAQ,GAAG;UACb7C,GAAGA,CAAC8C,WAAW,EAAEC,IAAI,EAAE;YACrB,OAAOA,IAAI,IAAI5B,MAAM,IAAI4B,IAAI,IAAIJ,KAAK;UACxC,CAAC;UAED7C,GAAGA,CAACgD,WAAW,EAAEC,IAAI,EAAEC,QAAQ,EAAE;YAC/B,IAAID,IAAI,IAAIJ,KAAK,EAAE;cACjB,OAAOA,KAAK,CAACI,IAAI,CAAC;YACpB;YAEA,IAAI,EAAEA,IAAI,IAAI5B,MAAM,CAAC,EAAE;cACrB,OAAOtB,SAAS;YAClB;YAEA,IAAIM,KAAK,GAAGgB,MAAM,CAAC4B,IAAI,CAAC;YAExB,IAAI,OAAO5C,KAAK,KAAK,UAAU,EAAE;cAC/B;cACA;;cAEA,IAAI,OAAOuC,QAAQ,CAACK,IAAI,CAAC,KAAK,UAAU,EAAE;gBACxC;gBACA5C,KAAK,GAAG0B,UAAU,CAACV,MAAM,EAAEA,MAAM,CAAC4B,IAAI,CAAC,EAAEL,QAAQ,CAACK,IAAI,CAAC,CAAC;cAC1D,CAAC,MAAM,IAAIV,cAAc,CAAC9B,QAAQ,EAAEwC,IAAI,CAAC,EAAE;gBACzC;gBACA;gBACA,IAAIhB,OAAO,GAAGf,iBAAiB,CAAC+B,IAAI,EAAExC,QAAQ,CAACwC,IAAI,CAAC,CAAC;gBACrD5C,KAAK,GAAG0B,UAAU,CAACV,MAAM,EAAEA,MAAM,CAAC4B,IAAI,CAAC,EAAEhB,OAAO,CAAC;cACnD,CAAC,MAAM;gBACL;gBACA;gBACA5B,KAAK,GAAGA,KAAK,CAACoC,IAAI,CAACpB,MAAM,CAAC;cAC5B;YACF,CAAC,MAAM,IAAI,OAAOhB,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,KAC1CkC,cAAc,CAACK,QAAQ,EAAEK,IAAI,CAAC,IAC9BV,cAAc,CAAC9B,QAAQ,EAAEwC,IAAI,CAAC,CAAC,EAAE;cAC3C;cACA;cACA;cACA5C,KAAK,GAAGsC,UAAU,CAACtC,KAAK,EAAEuC,QAAQ,CAACK,IAAI,CAAC,EAAExC,QAAQ,CAACwC,IAAI,CAAC,CAAC;YAC3D,CAAC,MAAM,IAAIV,cAAc,CAAC9B,QAAQ,EAAE,GAAG,CAAC,EAAE;cACxC;cACAJ,KAAK,GAAGsC,UAAU,CAACtC,KAAK,EAAEuC,QAAQ,CAACK,IAAI,CAAC,EAAExC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1D,CAAC,MAAM;cACL;cACA;cACAlB,MAAM,CAAC4D,cAAc,CAACN,KAAK,EAAEI,IAAI,EAAE;gBACjCG,YAAY,EAAE,IAAI;gBAClBC,UAAU,EAAE,IAAI;gBAChBrD,GAAGA,CAAA,EAAG;kBACJ,OAAOqB,MAAM,CAAC4B,IAAI,CAAC;gBACrB,CAAC;gBACD9C,GAAGA,CAACE,KAAK,EAAE;kBACTgB,MAAM,CAAC4B,IAAI,CAAC,GAAG5C,KAAK;gBACtB;cACF,CAAC,CAAC;cAEF,OAAOA,KAAK;YACd;YAEAwC,KAAK,CAACI,IAAI,CAAC,GAAG5C,KAAK;YACnB,OAAOA,KAAK;UACd,CAAC;UAEDF,GAAGA,CAAC6C,WAAW,EAAEC,IAAI,EAAE5C,KAAK,EAAE6C,QAAQ,EAAE;YACtC,IAAID,IAAI,IAAIJ,KAAK,EAAE;cACjBA,KAAK,CAACI,IAAI,CAAC,GAAG5C,KAAK;YACrB,CAAC,MAAM;cACLgB,MAAM,CAAC4B,IAAI,CAAC,GAAG5C,KAAK;YACtB;YACA,OAAO,IAAI;UACb,CAAC;UAED8C,cAAcA,CAACH,WAAW,EAAEC,IAAI,EAAEK,IAAI,EAAE;YACtC,OAAOC,OAAO,CAACJ,cAAc,CAACN,KAAK,EAAEI,IAAI,EAAEK,IAAI,CAAC;UAClD,CAAC;UAEDE,cAAcA,CAACR,WAAW,EAAEC,IAAI,EAAE;YAChC,OAAOM,OAAO,CAACC,cAAc,CAACX,KAAK,EAAEI,IAAI,CAAC;UAC5C;QACF,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAID,WAAW,GAAGzD,MAAM,CAACuD,MAAM,CAACzB,MAAM,CAAC;QACvC,OAAO,IAAIa,KAAK,CAACc,WAAW,EAAED,QAAQ,CAAC;MACzC,CAAC;;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAMU,SAAS,GAAGC,UAAU,KAAK;QAC/BC,WAAWA,CAACtC,MAAM,EAAEuC,QAAQ,EAAE,GAAGtC,IAAI,EAAE;UACrCD,MAAM,CAACsC,WAAW,CAACD,UAAU,CAAC1D,GAAG,CAAC4D,QAAQ,CAAC,EAAE,GAAGtC,IAAI,CAAC;QACvD,CAAC;QAEDuC,WAAWA,CAACxC,MAAM,EAAEuC,QAAQ,EAAE;UAC5B,OAAOvC,MAAM,CAACwC,WAAW,CAACH,UAAU,CAAC1D,GAAG,CAAC4D,QAAQ,CAAC,CAAC;QACrD,CAAC;QAEDE,cAAcA,CAACzC,MAAM,EAAEuC,QAAQ,EAAE;UAC/BvC,MAAM,CAACyC,cAAc,CAACJ,UAAU,CAAC1D,GAAG,CAAC4D,QAAQ,CAAC,CAAC;QACjD;MACF,CAAC,CAAC;MAEF,MAAMG,yBAAyB,GAAG,IAAIrE,cAAc,CAACkE,QAAQ,IAAI;QAC/D,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClC,OAAOA,QAAQ;QACjB;;QAEA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;QACM,OAAO,SAASI,iBAAiBA,CAACC,GAAG,EAAE;UACrC,MAAMC,UAAU,GAAGvB,UAAU,CAACsB,GAAG,EAAE,CAAC,CAAC,CAAC,gBAAgB;YACpDE,UAAU,EAAE;cACV5C,OAAO,EAAE,CAAC;cACVC,OAAO,EAAE;YACX;UACF,CAAC,CAAC;UACFoC,QAAQ,CAACM,UAAU,CAAC;QACtB,CAAC;MACH,CAAC,CAAC;MAEF,MAAME,iBAAiB,GAAG,IAAI1E,cAAc,CAACkE,QAAQ,IAAI;QACvD,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClC,OAAOA,QAAQ;QACjB;;QAEA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACM,OAAO,SAASS,SAASA,CAACxD,OAAO,EAAEyD,MAAM,EAAEC,YAAY,EAAE;UACvD,IAAIC,mBAAmB,GAAG,KAAK;UAE/B,IAAIC,mBAAmB;UACvB,IAAIC,mBAAmB,GAAG,IAAIjD,OAAO,CAACV,OAAO,IAAI;YAC/C0D,mBAAmB,GAAG,SAAAA,CAASE,QAAQ,EAAE;cACvCH,mBAAmB,GAAG,IAAI;cAC1BzD,OAAO,CAAC4D,QAAQ,CAAC;YACnB,CAAC;UACH,CAAC,CAAC;UAEF,IAAIC,MAAM;UACV,IAAI;YACFA,MAAM,GAAGhB,QAAQ,CAAC/C,OAAO,EAAEyD,MAAM,EAAEG,mBAAmB,CAAC;UACzD,CAAC,CAAC,OAAOI,GAAG,EAAE;YACZD,MAAM,GAAGnD,OAAO,CAACb,MAAM,CAACiE,GAAG,CAAC;UAC9B;UAEA,MAAMC,gBAAgB,GAAGF,MAAM,KAAK,IAAI,IAAIxE,UAAU,CAACwE,MAAM,CAAC;;UAE9D;UACA;UACA;UACA,IAAIA,MAAM,KAAK,IAAI,IAAI,CAACE,gBAAgB,IAAI,CAACN,mBAAmB,EAAE;YAChE,OAAO,KAAK;UACd;;UAEA;UACA;UACA;UACA;UACA,MAAMO,kBAAkB,GAAIvE,OAAO,IAAK;YACtCA,OAAO,CAACF,IAAI,CAAC0E,GAAG,IAAI;cAClB;cACAT,YAAY,CAACS,GAAG,CAAC;YACnB,CAAC,EAAEC,KAAK,IAAI;cACV;cACA;cACA,IAAIpE,OAAO;cACX,IAAIoE,KAAK,KAAKA,KAAK,YAAYhG,KAAK,IAChC,OAAOgG,KAAK,CAACpE,OAAO,KAAK,QAAQ,CAAC,EAAE;gBACtCA,OAAO,GAAGoE,KAAK,CAACpE,OAAO;cACzB,CAAC,MAAM;gBACLA,OAAO,GAAG,8BAA8B;cAC1C;cAEA0D,YAAY,CAAC;gBACXW,iCAAiC,EAAE,IAAI;gBACvCrE;cACF,CAAC,CAAC;YACJ,CAAC,CAAC,CAACsE,KAAK,CAACN,GAAG,IAAI;cACd;cACAjD,OAAO,CAACqD,KAAK,CAAC,yCAAyC,EAAEJ,GAAG,CAAC;YAC/D,CAAC,CAAC;UACJ,CAAC;;UAED;UACA;UACA;UACA,IAAIC,gBAAgB,EAAE;YACpBC,kBAAkB,CAACH,MAAM,CAAC;UAC5B,CAAC,MAAM;YACLG,kBAAkB,CAACL,mBAAmB,CAAC;UACzC;;UAEA;UACA,OAAO,IAAI;QACb,CAAC;MACH,CAAC,CAAC;MAEF,MAAMU,0BAA0B,GAAGA,CAAC;QAACxE,MAAM;QAAEG;MAAO,CAAC,EAAEsE,KAAK,KAAK;QAC/D,IAAIhG,aAAa,CAACN,OAAO,CAAC4B,SAAS,EAAE;UACnC;UACA;UACA;UACA,IAAItB,aAAa,CAACN,OAAO,CAAC4B,SAAS,CAACE,OAAO,KAAK1B,gDAAgD,EAAE;YAChG4B,OAAO,CAAC,CAAC;UACX,CAAC,MAAM;YACLH,MAAM,CAAC,IAAI3B,KAAK,CAACI,aAAa,CAACN,OAAO,CAAC4B,SAAS,CAACE,OAAO,CAAC,CAAC;UAC5D;QACF,CAAC,MAAM,IAAIwE,KAAK,IAAIA,KAAK,CAACH,iCAAiC,EAAE;UAC3D;UACA;UACAtE,MAAM,CAAC,IAAI3B,KAAK,CAACoG,KAAK,CAACxE,OAAO,CAAC,CAAC;QAClC,CAAC,MAAM;UACLE,OAAO,CAACsE,KAAK,CAAC;QAChB;MACF,CAAC;MAED,MAAMC,kBAAkB,GAAGA,CAACnE,IAAI,EAAEV,QAAQ,EAAE8E,eAAe,EAAE,GAAGjE,IAAI,KAAK;QACvE,IAAIA,IAAI,CAAC7B,MAAM,GAAGgB,QAAQ,CAACc,OAAO,EAAE;UAClC,MAAM,IAAItC,KAAK,CAAE,qBAAoBwB,QAAQ,CAACc,OAAQ,IAAGP,kBAAkB,CAACP,QAAQ,CAACc,OAAO,CAAE,QAAOJ,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAAC,CAAC;QACpI;QAEA,IAAI6B,IAAI,CAAC7B,MAAM,GAAGgB,QAAQ,CAACe,OAAO,EAAE;UAClC,MAAM,IAAIvC,KAAK,CAAE,oBAAmBwB,QAAQ,CAACe,OAAQ,IAAGR,kBAAkB,CAACP,QAAQ,CAACe,OAAO,CAAE,QAAOL,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAAC,CAAC;QACnI;QAEA,OAAO,IAAIgC,OAAO,CAAC,CAACV,OAAO,EAAEH,MAAM,KAAK;UACtC,MAAM4E,SAAS,GAAGJ,0BAA0B,CAAC3C,IAAI,CAAC,IAAI,EAAE;YAAC1B,OAAO;YAAEH;UAAM,CAAC,CAAC;UAC1EU,IAAI,CAACmE,IAAI,CAACD,SAAS,CAAC;UACpBD,eAAe,CAACG,WAAW,CAAC,GAAGpE,IAAI,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC;MAED,MAAMqE,cAAc,GAAG;QACrBC,QAAQ,EAAE;UACRC,OAAO,EAAE;YACP7B,iBAAiB,EAAEP,SAAS,CAACM,yBAAyB;UACxD;QACF,CAAC;QACDhF,OAAO,EAAE;UACPsF,SAAS,EAAEZ,SAAS,CAACW,iBAAiB,CAAC;UACvC0B,iBAAiB,EAAErC,SAAS,CAACW,iBAAiB,CAAC;UAC/CsB,WAAW,EAAEJ,kBAAkB,CAAC7C,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE;YAAClB,OAAO,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAC,CAAC;QACpF,CAAC;QACDuE,IAAI,EAAE;UACJL,WAAW,EAAEJ,kBAAkB,CAAC7C,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE;YAAClB,OAAO,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAC,CAAC;QACpF;MACF,CAAC;MACD,MAAMwE,eAAe,GAAG;QACtBC,KAAK,EAAE;UAAC1E,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAC,CAAC;QAC/BxB,GAAG,EAAE;UAACuB,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAC,CAAC;QAC7BrB,GAAG,EAAE;UAACoB,OAAO,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAC;MAC9B,CAAC;MACDlC,WAAW,CAAC4G,OAAO,GAAG;QACpBL,OAAO,EAAE;UAAC,GAAG,EAAEG;QAAe,CAAC;QAC/BG,QAAQ,EAAE;UAAC,GAAG,EAAEH;QAAe,CAAC;QAChCI,QAAQ,EAAE;UAAC,GAAG,EAAEJ;QAAe;MACjC,CAAC;MAED,OAAOrD,UAAU,CAACtD,aAAa,EAAEsG,cAAc,EAAErG,WAAW,CAAC;IAC/D,CAAC;;IAED;IACA;IACA+G,MAAM,CAACC,OAAO,GAAGlH,QAAQ,CAACN,MAAM,CAAC;EACnC,CAAC,MAAM;IACLuH,MAAM,CAACC,OAAO,GAAGzH,UAAU,CAACK,OAAO;EACrC;AAAC", "ignoreList": []}
const removeIds = (node) => {
  if (node.nodeType === Node.ELEMENT_NODE) {
    if (node.hasAttribute('id')) {
      node.removeAttribute('id');
    }
  }

  node.childNodes.forEach(removeIds);
};

// Initial removal of IDs on page load
removeIds(document.body);

// Observe DOM changes to remove IDs from dynamically added elements
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    mutation.addedNodes.forEach(removeIds);
  });
});

observer.observe(document.body, {
  childList: true,
  subtree: true,
});
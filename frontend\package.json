{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@types/react-syntax-highlighter": "^15.5.13", "lucide-react": "^0.525.0", "path": "^0.12.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-syntax-highlighter": "^15.6.1", "tailwindcss": "^4.1.11", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "terser": "^5.43.1", "vite": "^7.0.0"}}